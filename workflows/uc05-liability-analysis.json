{"name": "UC05 - Liability Analysis Workflow", "nodes": [{"parameters": {"path": "liability-webhook", "httpMethod": "POST", "responseMode": "responseNode", "options": {}}, "id": "webhook-start", "name": "Document Upload Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "liability-webhook"}, {"parameters": {"functionCode": "// Validate incoming documents\nconst documents = $input.all();\nconst validDocuments = [];\n\nfor (const doc of documents) {\n  if (doc.json.filename && doc.json.content) {\n    validDocuments.push({\n      filename: doc.json.filename,\n      content: doc.json.content,\n      size: doc.json.size || 0,\n      type: doc.json.type || 'unknown'\n    });\n  }\n}\n\nreturn {\n  json: {\n    documents: validDocuments,\n    count: validDocuments.length,\n    timestamp: new Date().toISOString()\n  }\n};"}, "id": "validate-docs", "name": "Validate Documents", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "http://backend:8000/api/v1/ocr/batch-process", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "documents", "value": "={{ $json.documents }}"}, {"name": "engine", "value": "minicpm-o"}]}, "options": {"timeout": 300000}}, "id": "ocr-processing", "name": "OCR Processing (MiniCPM-o)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"functionCode": "// Classify documents based on OCR results\nconst ocrResults = $input.all();\nconst classifiedDocs = [];\n\nfor (const result of ocrResults) {\n  const text = result.json.extracted_text || '';\n  const filename = result.json.filename || '';\n  \n  let docType = 'other';\n  \n  // Classification logic\n  if (filename.toLowerCase().includes('fnol') || \n      text.toLowerCase().includes('first notice of loss')) {\n    docType = 'fnol';\n  } else if (filename.toLowerCase().includes('email') || \n             text.toLowerCase().includes('from:') || \n             text.toLowerCase().includes('to:')) {\n    docType = 'email';\n  } else if (filename.toLowerCase().includes('certificate') || \n             text.toLowerCase().includes('certificate of insurance')) {\n    docType = 'certificate';\n  } else if (text.toLowerCase().includes('medical') || \n             text.toLowerCase().includes('doctor') || \n             text.toLowerCase().includes('hospital')) {\n    docType = 'medical_report';\n  } else if (text.toLowerCase().includes('incident') || \n             text.toLowerCase().includes('accident report')) {\n    docType = 'incident_report';\n  }\n  \n  classifiedDocs.push({\n    filename: result.json.filename,\n    text: text,\n    ocr_confidence: result.json.confidence || 0,\n    document_type: docType,\n    file_size: result.json.file_size || 0\n  });\n}\n\nreturn {\n  json: {\n    classified_documents: classifiedDocs,\n    total_documents: classifiedDocs.length\n  }\n};"}, "id": "classify-docs", "name": "Document Classification", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"model": "gpt-4o", "messages": {"values": [{"role": "system", "content": "You are an expert insurance liability analyst. Extract key evidence from insurance documents that would be relevant for determining fault allocation in liability claims."}, {"role": "user", "content": "Analyze these insurance documents and extract liability-relevant evidence:\n\n{{ $json.classified_documents }}\n\nFor each document, identify:\n1. Key facts about the incident\n2. Fault indicators\n3. Damages or injuries\n4. Environmental factors\n5. Legal considerations\n\nReturn as structured JSON with evidence items."}]}, "options": {"temperature": 0.3, "maxTokens": 2000}}, "id": "evidence-extraction", "name": "Evidence Extraction (GPT-4o)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"url": "http://backend:8000/api/v1/liability/calculate-fault", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "evidence", "value": "={{ $json.evidence }}"}, {"name": "documents", "value": "={{ $('Document Classification').item.json.classified_documents }}"}, {"name": "jurisdiction", "value": "canada"}]}}, "id": "fault-calculation", "name": "Fault Percentage Calculation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 300]}, {"parameters": {"functionCode": "// Calculate confidence score based on evidence quality\nconst faultResult = $input.all()[0].json;\nconst evidence = faultResult.evidence || [];\nconst documents = $('Document Classification').item.json.classified_documents || [];\n\n// Evidence quality factors\nlet evidenceScore = 0;\nlet totalEvidence = evidence.length;\n\nif (totalEvidence > 0) {\n  evidenceScore = evidence.reduce((sum, ev) => {\n    return sum + (ev.confidence || 0.5) * (ev.relevance_score || 0.5);\n  }, 0) / totalEvidence;\n}\n\n// Document quality factors\nlet docScore = 0;\nif (documents.length > 0) {\n  docScore = documents.reduce((sum, doc) => {\n    return sum + (doc.ocr_confidence || 0);\n  }, 0) / documents.length;\n}\n\n// Coverage factors\nlet coverageScore = 0.8; // Default\nconst hasPolicy = evidence.some(ev => ev.type === 'coverage_limits');\nconst hasFNOL = documents.some(doc => doc.document_type === 'fnol');\nconst hasIncident = documents.some(doc => doc.document_type === 'incident_report');\n\nif (hasPolicy) coverageScore += 0.1;\nif (hasFNOL) coverageScore += 0.1;\nif (hasIncident) coverageScore += 0.1;\n\n// Final confidence calculation\nconst overallConfidence = Math.min(\n  (evidenceScore * 0.4 + docScore * 0.3 + coverageScore * 0.3),\n  1.0\n);\n\nreturn {\n  json: {\n    ...faultResult,\n    confidence_score: Math.round(overallConfidence * 100) / 100,\n    confidence_factors: {\n      evidence_quality: Math.round(evidenceScore * 100) / 100,\n      document_quality: Math.round(docScore * 100) / 100,\n      coverage_completeness: Math.round(coverageScore * 100) / 100\n    }\n  }\n};"}, "id": "confidence-scoring", "name": "Confidence Scoring", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"functionCode": "// Generate final recommendation\nconst analysis = $input.all()[0].json;\nconst insuredFault = analysis.insured_fault_percentage || 50;\nconst confidence = analysis.confidence_score || 0.5;\n\nlet recommendation = 'review_required';\nlet reasoning = 'Standard review required';\nlet nextSteps = ['Review documentation', 'Assess coverage'];\n\n// Decision logic\nif (confidence < 0.6) {\n  recommendation = 'investigate_further';\n  reasoning = 'Low confidence score requires additional investigation';\n  nextSteps = [\n    'Obtain additional documentation',\n    'Consider expert consultation',\n    'Review legal precedents'\n  ];\n} else if (insuredFault <= 25) {\n  recommendation = 'deny_claim';\n  reasoning = 'Minimal insured fault suggests claim denial';\n  nextSteps = [\n    'Prepare denial letter',\n    'Document reasoning',\n    'Consider subrogation opportunities'\n  ];\n} else if (insuredFault <= 75) {\n  recommendation = 'negotiate_settlement';\n  reasoning = 'Shared fault suggests settlement negotiation';\n  nextSteps = [\n    'Calculate settlement range',\n    'Initiate negotiations',\n    'Review policy limits'\n  ];\n} else {\n  recommendation = 'accept_liability';\n  reasoning = 'High insured fault suggests accepting liability';\n  nextSteps = [\n    'Assess total exposure',\n    'Reserve appropriate amounts',\n    'Begin settlement process'\n  ];\n}\n\nreturn {\n  json: {\n    liability_assessment: {\n      insured_fault_percentage: insuredFault,\n      third_party_fault_percentage: 100 - insuredFault,\n      confidence_score: confidence,\n      recommendation: {\n        action: recommendation,\n        reasoning: reasoning,\n        next_steps: nextSteps\n      },\n      evidence_summary: analysis.evidence || [],\n      risk_factors: {\n        legal_complexity: confidence < 0.7 ? 'high' : 'medium',\n        evidence_quality: confidence > 0.8 ? 'strong' : 'moderate',\n        coverage_adequacy: 'requires_review'\n      },\n      processing_metadata: {\n        workflow_version: '1.0',\n        processed_at: new Date().toISOString(),\n        total_documents: $('Document Classification').item.json.total_documents,\n        ocr_engine: 'minicpm-o',\n        ai_model: 'gpt-4o'\n      }\n    }\n  }\n};"}, "id": "generate-recommendation", "name": "Generate Recommendation", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"url": "http://backend:8000/api/v1/results/save", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "job_id", "value": "={{ $('Document Upload Webhook').item.json.job_id }}"}, {"name": "use_case", "value": "uc05"}, {"name": "results", "value": "={{ $json.liability_assessment }}"}]}}, "id": "save-results", "name": "Save Results", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2000, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "webhook-response", "name": "Return Results", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2220, 300]}], "connections": {"Document Upload Webhook": {"main": [[{"node": "Validate Documents", "type": "main", "index": 0}]]}, "Validate Documents": {"main": [[{"node": "OCR Processing (MiniCPM-o)", "type": "main", "index": 0}]]}, "OCR Processing (MiniCPM-o)": {"main": [[{"node": "Document Classification", "type": "main", "index": 0}]]}, "Document Classification": {"main": [[{"node": "Evidence Extraction (GPT-4o)", "type": "main", "index": 0}]]}, "Evidence Extraction (GPT-4o)": {"main": [[{"node": "Fault Percentage Calculation", "type": "main", "index": 0}]]}, "Fault Percentage Calculation": {"main": [[{"node": "Confidence Scoring", "type": "main", "index": 0}]]}, "Confidence Scoring": {"main": [[{"node": "Generate Recommendation", "type": "main", "index": 0}]]}, "Generate Recommendation": {"main": [[{"node": "Save Results", "type": "main", "index": 0}]]}, "Save Results": {"main": [[{"node": "Return Results", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "America/Toronto"}, "versionId": "1.0.0", "meta": {"templateCredsSetupCompleted": true}, "id": "uc05-liability-workflow", "tags": [{"createdAt": "2025-06-18T00:00:00.000Z", "updatedAt": "2025-06-18T00:00:00.000Z", "id": "liability", "name": "liability"}, {"createdAt": "2025-06-18T00:00:00.000Z", "updatedAt": "2025-06-18T00:00:00.000Z", "id": "uc05", "name": "uc05"}]}