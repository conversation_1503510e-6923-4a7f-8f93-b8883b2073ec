# 🏆 ZURICH CHALLENGE - ENVIRONMENT CONFIGURATION
# Copy this file to .env and update with your values

# =============================================================================
# API KEYS (REQUIRED FOR AI ANALYSIS)
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=*******************************************/zurich_db
POSTGRES_DB=zurich_db
POSTGRES_USER=zurich
POSTGRES_PASSWORD=zurich123

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=redis123

# =============================================================================
# MINIO S3 STORAGE
# =============================================================================
MINIO_URL=http://minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET=zurich-documents

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-change-in-production

# =============================================================================
# OCR CONFIGURATION
# =============================================================================
OCR_ENGINE=minicpm-o
OCR_CONFIDENCE_THRESHOLD=0.8

# =============================================================================
# PROCESSING LIMITS
# =============================================================================
MAX_FILE_SIZE=52428800
MAX_WORKERS=4
BATCH_SIZE=10
TIMEOUT_SECONDS=300

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================
CELERY_BROKER_URL=redis://redis:6379
CELERY_RESULT_BACKEND=redis://redis:6379

# =============================================================================
# N8N CONFIGURATION
# =============================================================================
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=zurich123
WEBHOOK_URL=http://localhost:5678

# =============================================================================
# MONITORING
# =============================================================================
ENABLE_METRICS=true
GRAFANA_ADMIN_PASSWORD=zurich123

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
