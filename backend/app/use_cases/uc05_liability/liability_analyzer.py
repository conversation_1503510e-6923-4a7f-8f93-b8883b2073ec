"""
🎯 UC05: Liability Decisions Analyzer
Advanced AI-powered liability assessment for insurance claims
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import re
import json

from app.services.ai_service import AIService
from app.core.config import settings

logger = logging.getLogger(__name__)

class DocumentType(Enum):
    """Document types for liability analysis"""
    FNOL = "first_notice_of_loss"
    EMAIL = "email_communication"
    CERTIFICATE = "insurance_certificate"
    MEDICAL_REPORT = "medical_report"
    INCIDENT_REPORT = "incident_report"
    WITNESS_STATEMENT = "witness_statement"
    POLICE_REPORT = "police_report"
    PHOTO = "photo_evidence"
    OTHER = "other"

@dataclass
class Evidence:
    """Evidence extracted from documents"""
    type: str
    content: str
    confidence: float
    source_document: str
    relevance_score: float

@dataclass
class LiabilityAssessment:
    """Liability assessment result"""
    insured_fault_percentage: float
    third_party_fault_percentage: float
    confidence_score: float
    supporting_evidence: List[Evidence]
    risk_factors: Dict[str, Any]
    recommendation: Dict[str, Any]
    reasoning: str

class LiabilityAnalyzer:
    """Advanced liability analyzer for UC05"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.evidence_patterns = self._load_evidence_patterns()
        self.legal_precedents = self._load_legal_precedents()
    
    async def analyze_liability(
        self,
        documents: List[Dict[str, Any]],
        claim_context: Optional[Dict[str, Any]] = None
    ) -> LiabilityAssessment:
        """
        Analyze liability based on extracted documents
        
        Args:
            documents: List of processed documents with OCR text
            claim_context: Additional context about the claim
            
        Returns:
            LiabilityAssessment with fault percentages and reasoning
        """
        try:
            logger.info(f"🔍 Analyzing liability for {len(documents)} documents")
            
            # Step 1: Classify and extract evidence
            evidence_list = await self._extract_evidence(documents)
            
            # Step 2: Analyze incident circumstances
            incident_analysis = await self._analyze_incident(evidence_list, claim_context)
            
            # Step 3: Assess fault allocation
            fault_assessment = await self._assess_fault(incident_analysis, evidence_list)
            
            # Step 4: Calculate confidence score
            confidence = await self._calculate_confidence(evidence_list, fault_assessment)
            
            # Step 5: Generate recommendations
            recommendation = await self._generate_recommendation(fault_assessment, confidence)
            
            # Step 6: Compile final assessment
            assessment = LiabilityAssessment(
                insured_fault_percentage=fault_assessment["insured_fault"],
                third_party_fault_percentage=fault_assessment["third_party_fault"],
                confidence_score=confidence,
                supporting_evidence=evidence_list,
                risk_factors=fault_assessment["risk_factors"],
                recommendation=recommendation,
                reasoning=fault_assessment["reasoning"]
            )
            
            logger.info(f"✅ Liability analysis complete: {assessment.insured_fault_percentage}% insured fault")
            return assessment
            
        except Exception as e:
            logger.error(f"❌ Liability analysis failed: {e}")
            raise
    
    async def _extract_evidence(self, documents: List[Dict[str, Any]]) -> List[Evidence]:
        """Extract relevant evidence from documents"""
        evidence_list = []
        
        for doc in documents:
            try:
                # Classify document type
                doc_type = await self._classify_document(doc)
                
                # Extract evidence based on document type
                if doc_type == DocumentType.FNOL:
                    evidence = await self._extract_fnol_evidence(doc)
                elif doc_type == DocumentType.EMAIL:
                    evidence = await self._extract_email_evidence(doc)
                elif doc_type == DocumentType.CERTIFICATE:
                    evidence = await self._extract_certificate_evidence(doc)
                elif doc_type == DocumentType.MEDICAL_REPORT:
                    evidence = await self._extract_medical_evidence(doc)
                elif doc_type == DocumentType.INCIDENT_REPORT:
                    evidence = await self._extract_incident_evidence(doc)
                else:
                    evidence = await self._extract_general_evidence(doc)
                
                evidence_list.extend(evidence)
                
            except Exception as e:
                logger.warning(f"⚠️ Failed to extract evidence from document: {e}")
                continue
        
        return evidence_list
    
    async def _classify_document(self, document: Dict[str, Any]) -> DocumentType:
        """Classify document type using AI"""
        text = document.get("text", "")
        filename = document.get("filename", "")
        
        # Use AI to classify document
        prompt = f"""
        Classify this insurance document into one of these types:
        - FNOL (First Notice of Loss)
        - EMAIL (Email communication)
        - CERTIFICATE (Insurance certificate)
        - MEDICAL_REPORT (Medical report/records)
        - INCIDENT_REPORT (Incident report)
        - WITNESS_STATEMENT (Witness statement)
        - POLICE_REPORT (Police report)
        - PHOTO (Photo evidence)
        - OTHER (Other document)
        
        Filename: {filename}
        Text sample: {text[:500]}...
        
        Return only the classification type.
        """
        
        try:
            classification = await self.ai_service.generate_response(prompt)
            classification = classification.strip().upper()
            
            # Map to enum
            for doc_type in DocumentType:
                if doc_type.name in classification:
                    return doc_type
            
            return DocumentType.OTHER
            
        except Exception as e:
            logger.warning(f"⚠️ Document classification failed: {e}")
            return DocumentType.OTHER
    
    async def _extract_fnol_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from First Notice of Loss"""
        text = document.get("text", "")
        
        prompt = f"""
        Extract key liability evidence from this First Notice of Loss document:
        
        {text}
        
        Focus on:
        1. Incident location and time
        2. Description of what happened
        3. Parties involved
        4. Injuries or damages
        5. Fault indicators
        6. Weather/environmental conditions
        
        Return as JSON array with format:
        [{{
            "type": "incident_location",
            "content": "extracted information",
            "relevance_score": 0.8
        }}]
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            evidence_data = json.loads(response)
            
            evidence_list = []
            for item in evidence_data:
                evidence = Evidence(
                    type=item["type"],
                    content=item["content"],
                    confidence=0.85,  # FNOL typically reliable
                    source_document=document.get("filename", "FNOL"),
                    relevance_score=item.get("relevance_score", 0.7)
                )
                evidence_list.append(evidence)
            
            return evidence_list
            
        except Exception as e:
            logger.warning(f"⚠️ FNOL evidence extraction failed: {e}")
            return []
    
    async def _extract_email_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from email communications"""
        text = document.get("text", "")
        
        # Look for key patterns in emails
        evidence_list = []
        
        # Settlement discussions
        if re.search(r'settlement|settle|pay|payment|compensation', text, re.IGNORECASE):
            evidence_list.append(Evidence(
                type="settlement_discussion",
                content="Email contains settlement discussions",
                confidence=0.9,
                source_document=document.get("filename", "Email"),
                relevance_score=0.8
            ))
        
        # Admission of fault
        fault_patterns = [
            r'my fault', r'i was wrong', r'i caused', r'i am responsible',
            r'sorry for', r'apologize for', r'my mistake'
        ]
        
        for pattern in fault_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                evidence_list.append(Evidence(
                    type="fault_admission",
                    content=f"Potential fault admission found: {pattern}",
                    confidence=0.7,
                    source_document=document.get("filename", "Email"),
                    relevance_score=0.9
                ))
                break
        
        return evidence_list
    
    async def _extract_certificate_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from insurance certificates"""
        text = document.get("text", "")
        evidence_list = []
        
        # Extract coverage limits
        coverage_pattern = r'\$[\d,]+(?:\.\d{2})?'
        coverage_matches = re.findall(coverage_pattern, text)
        
        if coverage_matches:
            evidence_list.append(Evidence(
                type="coverage_limits",
                content=f"Coverage limits found: {', '.join(coverage_matches)}",
                confidence=0.95,
                source_document=document.get("filename", "Certificate"),
                relevance_score=0.6
            ))
        
        # Extract policy period
        date_pattern = r'\d{1,2}[-/]\d{1,2}[-/]\d{4}'
        dates = re.findall(date_pattern, text)
        
        if len(dates) >= 2:
            evidence_list.append(Evidence(
                type="policy_period",
                content=f"Policy period: {dates[0]} to {dates[1]}",
                confidence=0.9,
                source_document=document.get("filename", "Certificate"),
                relevance_score=0.5
            ))
        
        return evidence_list
    
    async def _extract_medical_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from medical reports"""
        text = document.get("text", "")
        evidence_list = []
        
        # Look for injury descriptions
        injury_keywords = [
            'fracture', 'broken', 'sprain', 'strain', 'contusion',
            'laceration', 'concussion', 'whiplash', 'herniation'
        ]
        
        for keyword in injury_keywords:
            if keyword in text.lower():
                evidence_list.append(Evidence(
                    type="injury_type",
                    content=f"Injury type: {keyword}",
                    confidence=0.8,
                    source_document=document.get("filename", "Medical"),
                    relevance_score=0.7
                ))
        
        return evidence_list
    
    async def _extract_incident_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract evidence from incident reports"""
        text = document.get("text", "")
        
        prompt = f"""
        Extract liability-relevant information from this incident report:
        
        {text}
        
        Focus on:
        1. Sequence of events
        2. Contributing factors
        3. Safety violations
        4. Environmental conditions
        5. Witness observations
        
        Return as JSON array.
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            evidence_data = json.loads(response)
            
            evidence_list = []
            for item in evidence_data:
                evidence = Evidence(
                    type=item["type"],
                    content=item["content"],
                    confidence=0.8,
                    source_document=document.get("filename", "Incident Report"),
                    relevance_score=item.get("relevance_score", 0.7)
                )
                evidence_list.append(evidence)
            
            return evidence_list
            
        except Exception as e:
            logger.warning(f"⚠️ Incident evidence extraction failed: {e}")
            return []
    
    async def _extract_general_evidence(self, document: Dict[str, Any]) -> List[Evidence]:
        """Extract general evidence from any document"""
        text = document.get("text", "")
        evidence_list = []
        
        # Look for key liability indicators
        liability_keywords = {
            'negligence': 0.8,
            'breach of duty': 0.9,
            'reasonable care': 0.7,
            'proximate cause': 0.8,
            'contributory negligence': 0.9
        }
        
        for keyword, relevance in liability_keywords.items():
            if keyword in text.lower():
                evidence_list.append(Evidence(
                    type="legal_concept",
                    content=f"Legal concept mentioned: {keyword}",
                    confidence=0.6,
                    source_document=document.get("filename", "Document"),
                    relevance_score=relevance
                ))
        
        return evidence_list
    
    def _load_evidence_patterns(self) -> Dict[str, Any]:
        """Load evidence extraction patterns"""
        return {
            "fault_indicators": [
                "speeding", "distracted", "intoxicated", "reckless",
                "violation", "failed to", "negligent", "careless"
            ],
            "injury_severity": [
                "minor", "moderate", "severe", "critical", "fatal"
            ],
            "environmental_factors": [
                "weather", "visibility", "road conditions", "lighting"
            ]
        }
    
    def _load_legal_precedents(self) -> Dict[str, Any]:
        """Load Canadian legal precedents for liability"""
        return {
            "comparative_negligence": {
                "description": "Canadian law follows comparative negligence",
                "application": "Fault can be split between parties"
            },
            "duty_of_care": {
                "description": "Standard duty of care requirements",
                "application": "Must prove breach of reasonable care"
            }
        }
    
    async def _analyze_incident(
        self, 
        evidence_list: List[Evidence], 
        claim_context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze the incident based on evidence"""
        
        # Compile evidence for analysis
        evidence_summary = "\n".join([
            f"- {ev.type}: {ev.content} (confidence: {ev.confidence})"
            for ev in evidence_list
        ])
        
        prompt = f"""
        Analyze this insurance liability incident based on the evidence:
        
        Evidence:
        {evidence_summary}
        
        Additional Context: {claim_context or 'None provided'}
        
        Provide analysis covering:
        1. Sequence of events
        2. Contributing factors
        3. Applicable legal standards
        4. Comparative fault considerations
        
        Return as structured JSON.
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            return json.loads(response)
        except Exception as e:
            logger.warning(f"⚠️ Incident analysis failed: {e}")
            return {"error": str(e)}
    
    async def _assess_fault(
        self, 
        incident_analysis: Dict[str, Any], 
        evidence_list: List[Evidence]
    ) -> Dict[str, Any]:
        """Assess fault allocation between parties"""
        
        # High-relevance evidence gets more weight
        weighted_evidence = [
            ev for ev in evidence_list 
            if ev.relevance_score > 0.7
        ]
        
        prompt = f"""
        Based on this incident analysis and evidence, determine fault allocation:
        
        Incident Analysis: {json.dumps(incident_analysis, indent=2)}
        
        High-Priority Evidence:
        {chr(10).join([f"- {ev.type}: {ev.content}" for ev in weighted_evidence])}
        
        Apply Canadian comparative negligence law to determine:
        1. Insured party fault percentage (0-100%)
        2. Third party fault percentage (0-100%)
        3. Key reasoning for allocation
        4. Risk factors that could affect the assessment
        
        Return as JSON:
        {{
            "insured_fault": 65,
            "third_party_fault": 35,
            "reasoning": "detailed explanation",
            "risk_factors": {{
                "coverage_adequacy": "sufficient/insufficient",
                "legal_complexity": "low/medium/high",
                "evidence_quality": "strong/moderate/weak"
            }}
        }}
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            fault_data = json.loads(response)
            
            # Ensure percentages add up to 100
            total = fault_data["insured_fault"] + fault_data["third_party_fault"]
            if total != 100:
                # Normalize
                fault_data["insured_fault"] = round(fault_data["insured_fault"] * 100 / total)
                fault_data["third_party_fault"] = 100 - fault_data["insured_fault"]
            
            return fault_data
            
        except Exception as e:
            logger.warning(f"⚠️ Fault assessment failed: {e}")
            return {
                "insured_fault": 50,
                "third_party_fault": 50,
                "reasoning": "Unable to determine fault allocation due to insufficient evidence",
                "risk_factors": {
                    "coverage_adequacy": "unknown",
                    "legal_complexity": "high",
                    "evidence_quality": "weak"
                }
            }
    
    async def _calculate_confidence(
        self, 
        evidence_list: List[Evidence], 
        fault_assessment: Dict[str, Any]
    ) -> float:
        """Calculate confidence score for the assessment"""
        
        # Factors affecting confidence
        evidence_quality = sum(ev.confidence * ev.relevance_score for ev in evidence_list) / len(evidence_list) if evidence_list else 0
        evidence_quantity = min(len(evidence_list) / 10, 1.0)  # Normalize to 1.0
        
        # Risk factors impact
        risk_factors = fault_assessment.get("risk_factors", {})
        evidence_strength = 1.0 if risk_factors.get("evidence_quality") == "strong" else 0.7
        legal_clarity = 1.0 if risk_factors.get("legal_complexity") == "low" else 0.8
        
        # Calculate overall confidence
        confidence = (evidence_quality * 0.4 + 
                     evidence_quantity * 0.2 + 
                     evidence_strength * 0.2 + 
                     legal_clarity * 0.2)
        
        return round(confidence, 2)
    
    async def _generate_recommendation(
        self, 
        fault_assessment: Dict[str, Any], 
        confidence: float
    ) -> Dict[str, Any]:
        """Generate recommendations based on assessment"""
        
        insured_fault = fault_assessment["insured_fault"]
        
        if confidence < 0.6:
            action = "investigate_further"
            reasoning = "Low confidence requires additional investigation"
        elif insured_fault <= 25:
            action = "deny_claim"
            reasoning = "Minimal insured fault suggests claim denial"
        elif insured_fault <= 75:
            action = "negotiate_settlement"
            reasoning = "Shared fault suggests settlement negotiation"
        else:
            action = "accept_liability"
            reasoning = "High insured fault suggests accepting liability"
        
        return {
            "action": action,
            "reasoning": reasoning,
            "next_steps": [
                "Review coverage limits",
                "Assess potential exposure",
                "Consider legal consultation" if confidence < 0.7 else "Proceed with recommendation"
            ],
            "estimated_exposure": "TBD - requires coverage analysis"
        }
