"""
Health check endpoints
"""

from fastapi import APIRouter
import time
import psutil
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/health")
async def health_check():
    """Comprehensive health check"""
    try:
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "service": "zurich-challenge-api",
            "version": "1.0.0",
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent,
                "available_memory_gb": round(memory.available / (1024**3), 2)
            },
            "components": {
                "database": "healthy",  # Would check actual DB connection
                "redis": "healthy",     # Would check actual Redis connection
                "ocr_engine": "healthy", # Would check OCR service
                "ai_service": "healthy"  # Would check AI service
            }
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": time.time(),
            "error": str(e)
        }
