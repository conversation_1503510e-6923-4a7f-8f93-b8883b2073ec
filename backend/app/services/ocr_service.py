"""
🤖 OCR Service - MiniCPM-o Integration
State-of-the-art OCR with fallback engines
"""

import asyncio
import logging
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
import torch
from PIL import Image
import numpy as np
from transformers import AutoModel, AutoTokenizer
import io
import base64

from app.core.config import settings

logger = logging.getLogger(__name__)

class OCRService:
    """Advanced OCR service with MiniCPM-o and fallbacks"""
    
    def __init__(self):
        self.primary_engine = None
        self.fallback_engines = {}
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"OCR Service initialized on device: {self.device}")
    
    async def warm_up(self):
        """Initialize and warm up OCR engines"""
        try:
            # Load MiniCPM-o (primary engine)
            await self._load_minicpm_o()
            
            # Load fallback engines
            await self._load_fallback_engines()
            
            logger.info("✅ OCR engines warmed up successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to warm up OCR engines: {e}")
            raise
    
    async def _load_minicpm_o(self):
        """Load MiniCPM-o model"""
        try:
            logger.info("📥 Loading MiniCPM-o model...")
            
            model_name = "openbmb/MiniCPM-o-2_6"
            
            # Load tokenizer and model
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True,
                cache_dir=settings.MODELS_DIR
            )
            
            self.primary_engine = AutoModel.from_pretrained(
                model_name,
                trust_remote_code=True,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                cache_dir=settings.MODELS_DIR
            )
            
            if self.device == "cuda":
                self.primary_engine = self.primary_engine.cuda()
            
            logger.info("✅ MiniCPM-o model loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to load MiniCPM-o: {e}")
            # Continue with fallback engines
    
    async def _load_fallback_engines(self):
        """Load fallback OCR engines"""
        try:
            # PaddleOCR
            from paddleocr import PaddleOCR
            self.fallback_engines['paddleocr'] = PaddleOCR(
                use_angle_cls=True,
                lang='en',
                use_gpu=self.device == "cuda"
            )
            logger.info("✅ PaddleOCR loaded")
            
        except Exception as e:
            logger.warning(f"⚠️ PaddleOCR not available: {e}")
        
        try:
            # Tesseract
            import pytesseract
            self.fallback_engines['tesseract'] = pytesseract
            logger.info("✅ Tesseract loaded")
            
        except Exception as e:
            logger.warning(f"⚠️ Tesseract not available: {e}")
    
    async def extract_text(
        self,
        image: Union[str, Path, Image.Image, bytes],
        engine: str = "auto",
        languages: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Extract text from image using specified OCR engine
        
        Args:
            image: Image file path, PIL Image, or bytes
            engine: OCR engine to use ("auto", "minicpm-o", "paddleocr", "tesseract")
            languages: List of languages to detect
            
        Returns:
            Dict with extracted text, confidence, and metadata
        """
        try:
            # Prepare image
            pil_image = await self._prepare_image(image)
            
            # Choose engine
            if engine == "auto":
                engine = self._select_best_engine(pil_image)
            
            # Extract text
            if engine == "minicpm-o" and self.primary_engine:
                result = await self._extract_with_minicpm_o(pil_image)
            elif engine == "paddleocr" and "paddleocr" in self.fallback_engines:
                result = await self._extract_with_paddleocr(pil_image)
            elif engine == "tesseract" and "tesseract" in self.fallback_engines:
                result = await self._extract_with_tesseract(pil_image, languages)
            else:
                # Fallback to any available engine
                result = await self._extract_with_fallback(pil_image)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ OCR extraction failed: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "engine": "none",
                "error": str(e)
            }
    
    async def _prepare_image(self, image: Union[str, Path, Image.Image, bytes]) -> Image.Image:
        """Prepare image for OCR processing"""
        if isinstance(image, (str, Path)):
            return Image.open(image).convert("RGB")
        elif isinstance(image, bytes):
            return Image.open(io.BytesIO(image)).convert("RGB")
        elif isinstance(image, Image.Image):
            return image.convert("RGB")
        else:
            raise ValueError(f"Unsupported image type: {type(image)}")
    
    def _select_best_engine(self, image: Image.Image) -> str:
        """Select the best OCR engine based on image characteristics"""
        # For now, prefer MiniCPM-o if available
        if self.primary_engine:
            return "minicpm-o"
        elif "paddleocr" in self.fallback_engines:
            return "paddleocr"
        elif "tesseract" in self.fallback_engines:
            return "tesseract"
        else:
            raise RuntimeError("No OCR engines available")
    
    async def _extract_with_minicpm_o(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text using MiniCPM-o"""
        try:
            # Prepare prompt for OCR
            prompt = "Extract all text from this image. Maintain the original structure and formatting."
            
            # Convert image to base64 for the model
            buffered = io.BytesIO()
            image.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()
            
            # Run inference
            with torch.no_grad():
                response = self.primary_engine.chat(
                    image=image,
                    msgs=[{"role": "user", "content": prompt}],
                    tokenizer=self.tokenizer
                )
            
            return {
                "text": response,
                "confidence": 0.95,  # MiniCPM-o typically has high confidence
                "engine": "minicpm-o",
                "metadata": {
                    "model": "MiniCPM-o-2.6",
                    "device": self.device
                }
            }
            
        except Exception as e:
            logger.error(f"❌ MiniCPM-o extraction failed: {e}")
            raise
    
    async def _extract_with_paddleocr(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text using PaddleOCR"""
        try:
            # Convert PIL to numpy array
            img_array = np.array(image)
            
            # Run OCR
            result = self.fallback_engines['paddleocr'].ocr(img_array, cls=True)
            
            # Parse results
            text_lines = []
            total_confidence = 0
            count = 0
            
            for line in result[0] if result[0] else []:
                if len(line) >= 2:
                    text = line[1][0]
                    confidence = line[1][1]
                    text_lines.append(text)
                    total_confidence += confidence
                    count += 1
            
            extracted_text = "\n".join(text_lines)
            avg_confidence = total_confidence / count if count > 0 else 0
            
            return {
                "text": extracted_text,
                "confidence": avg_confidence,
                "engine": "paddleocr",
                "metadata": {
                    "lines_detected": count,
                    "language": "en"
                }
            }
            
        except Exception as e:
            logger.error(f"❌ PaddleOCR extraction failed: {e}")
            raise
    
    async def _extract_with_tesseract(
        self, 
        image: Image.Image, 
        languages: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Extract text using Tesseract"""
        try:
            import pytesseract
            
            # Set language
            lang = "+".join(languages) if languages else "eng+fra"
            
            # Extract text
            text = pytesseract.image_to_string(image, lang=lang)
            
            # Get confidence data
            data = pytesseract.image_to_data(image, lang=lang, output_type=pytesseract.Output.DICT)
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            return {
                "text": text.strip(),
                "confidence": avg_confidence / 100,  # Convert to 0-1 scale
                "engine": "tesseract",
                "metadata": {
                    "language": lang,
                    "words_detected": len(confidences)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Tesseract extraction failed: {e}")
            raise
    
    async def _extract_with_fallback(self, image: Image.Image) -> Dict[str, Any]:
        """Try all available engines as fallback"""
        engines = ["paddleocr", "tesseract"]
        
        for engine in engines:
            if engine in self.fallback_engines:
                try:
                    if engine == "paddleocr":
                        return await self._extract_with_paddleocr(image)
                    elif engine == "tesseract":
                        return await self._extract_with_tesseract(image)
                except Exception as e:
                    logger.warning(f"⚠️ {engine} failed, trying next: {e}")
                    continue
        
        raise RuntimeError("All OCR engines failed")
    
    async def batch_extract(
        self,
        images: List[Union[str, Path, Image.Image, bytes]],
        engine: str = "auto"
    ) -> List[Dict[str, Any]]:
        """Extract text from multiple images in batch"""
        tasks = [
            self.extract_text(image, engine)
            for image in images
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "text": "",
                    "confidence": 0.0,
                    "engine": "none",
                    "error": str(result),
                    "image_index": i
                })
            else:
                result["image_index"] = i
                processed_results.append(result)
        
        return processed_results
