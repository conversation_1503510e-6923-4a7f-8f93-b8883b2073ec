"""
AI Service for LLM integration (OpenAI GPT-4o + Anthropic Claude)
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
import openai
import anthropic
from app.core.config import settings

logger = logging.getLogger(__name__)

class AIService:
    """Advanced AI service with multiple LLM providers"""
    
    def __init__(self):
        self.openai_client = None
        self.anthropic_client = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize AI clients"""
        try:
            if settings.OPENAI_API_KEY:
                self.openai_client = openai.AsyncOpenAI(
                    api_key=settings.OPENAI_API_KEY
                )
                logger.info("✅ OpenAI client initialized")
            
            if settings.ANTHROPIC_API_KEY:
                self.anthropic_client = anthropic.AsyncAnthropic(
                    api_key=settings.ANTHROPIC_API_KEY
                )
                logger.info("✅ Anthropic client initialized")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize AI clients: {e}")
    
    async def warm_up(self):
        """Warm up AI models"""
        try:
            if self.openai_client:
                await self.generate_response("Test", provider="openai")
            if self.anthropic_client:
                await self.generate_response("Test", provider="anthropic")
            logger.info("✅ AI models warmed up")
        except Exception as e:
            logger.warning(f"⚠️ AI warm-up failed: {e}")
    
    async def generate_response(
        self,
        prompt: str,
        provider: str = "auto",
        model: Optional[str] = None,
        temperature: float = 0.3,
        max_tokens: int = 2000
    ) -> str:
        """Generate AI response"""
        try:
            if provider == "auto":
                provider = "openai" if self.openai_client else "anthropic"
            
            if provider == "openai" and self.openai_client:
                return await self._generate_openai_response(
                    prompt, model or "gpt-4o", temperature, max_tokens
                )
            elif provider == "anthropic" and self.anthropic_client:
                return await self._generate_anthropic_response(
                    prompt, model or "claude-3-5-sonnet-20241022", temperature, max_tokens
                )
            else:
                raise ValueError(f"Provider {provider} not available")
                
        except Exception as e:
            logger.error(f"❌ AI generation failed: {e}")
            raise
    
    async def _generate_openai_response(
        self, prompt: str, model: str, temperature: float, max_tokens: int
    ) -> str:
        """Generate response using OpenAI"""
        try:
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"❌ OpenAI request failed: {e}")
            raise
    
    async def _generate_anthropic_response(
        self, prompt: str, model: str, temperature: float, max_tokens: int
    ) -> str:
        """Generate response using Anthropic"""
        try:
            response = await self.anthropic_client.messages.create(
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
            
        except Exception as e:
            logger.error(f"❌ Anthropic request failed: {e}")
            raise
