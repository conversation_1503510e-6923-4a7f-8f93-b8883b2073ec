#!/bin/bash

# 🏆 ZURICH CHALLENGE - INSTANT SETUP SCRIPT
# Get the winning solution running in 2 minutes!

set -e

echo "🏆 ZURICH CHALLENGE - WINNING SOLUTION SETUP"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 Checking system requirements...${NC}"

# Check for NVIDIA GPU (optional but recommended)
if command -v nvidia-smi &> /dev/null; then
    echo -e "${GREEN}✅ NVIDIA GPU detected - OCR will be accelerated${NC}"
    GPU_AVAILABLE=true
else
    echo -e "${YELLOW}⚠️  No NVIDIA GPU detected - OCR will run on CPU${NC}"
    GPU_AVAILABLE=false
fi

# Create necessary directories
echo -e "${BLUE}📁 Creating project structure...${NC}"
mkdir -p data/{uploads,processed,exports,models}
mkdir -p logs
mkdir -p workflows
mkdir -p monitoring/{prometheus,grafana/{dashboards,datasources}}

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${BLUE}⚙️  Creating environment configuration...${NC}"
    cat > .env << EOF
# API Keys (Add your keys here)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here

# Database
POSTGRES_DB=zurich_db
POSTGRES_USER=zurich
POSTGRES_PASSWORD=zurich123

# Redis
REDIS_PASSWORD=redis123

# MinIO
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin123

# Application
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
EOF
    echo -e "${YELLOW}⚠️  Please edit .env file and add your API keys${NC}"
fi

# Download MiniCPM-o model (if not exists)
echo -e "${BLUE}🤖 Checking AI models...${NC}"
if [ ! -d "models/minicpm-o" ]; then
    echo -e "${BLUE}📥 Downloading MiniCPM-o model (this may take a few minutes)...${NC}"
    mkdir -p models/minicpm-o
    # Model will be downloaded on first run
    echo "Model will be downloaded automatically on first use"
fi

# Build and start services
echo -e "${BLUE}🐳 Building Docker containers...${NC}"
docker-compose build --parallel

echo -e "${BLUE}🚀 Starting all services...${NC}"
docker-compose up -d

# Wait for services to be ready
echo -e "${BLUE}⏳ Waiting for services to start...${NC}"
sleep 30

# Check service health
echo -e "${BLUE}🔍 Checking service health...${NC}"

# Check backend
if curl -s http://localhost:8000/health > /dev/null; then
    echo -e "${GREEN}✅ Backend API is running${NC}"
else
    echo -e "${RED}❌ Backend API is not responding${NC}"
fi

# Check frontend
if curl -s http://localhost:3000 > /dev/null; then
    echo -e "${GREEN}✅ Frontend is running${NC}"
else
    echo -e "${RED}❌ Frontend is not responding${NC}"
fi

# Check n8n
if curl -s http://localhost:5678 > /dev/null; then
    echo -e "${GREEN}✅ n8n Workflow Engine is running${NC}"
else
    echo -e "${RED}❌ n8n is not responding${NC}"
fi

# Load sample data
echo -e "${BLUE}📊 Loading sample data...${NC}"
if [ -f "scripts/load-samples.sh" ]; then
    ./scripts/load-samples.sh
fi

# Setup monitoring dashboards
echo -e "${BLUE}📈 Setting up monitoring...${NC}"
if [ -f "scripts/setup-monitoring.sh" ]; then
    ./scripts/setup-monitoring.sh
fi

echo ""
echo -e "${GREEN}🎉 ZURICH CHALLENGE SOLUTION IS READY!${NC}"
echo "=============================================="
echo ""
echo -e "${BLUE}🌐 Access Points:${NC}"
echo -e "   Main Application: ${GREEN}http://localhost:3000${NC}"
echo -e "   API Documentation: ${GREEN}http://localhost:8000/docs${NC}"
echo -e "   n8n Workflows: ${GREEN}http://localhost:5678${NC} (admin/zurich123)"
echo -e "   Monitoring: ${GREEN}http://localhost:3001${NC} (admin/zurich123)"
echo ""
echo -e "${BLUE}📚 Quick Commands:${NC}"
echo -e "   View logs: ${YELLOW}docker-compose logs -f${NC}"
echo -e "   Stop services: ${YELLOW}docker-compose down${NC}"
echo -e "   Restart: ${YELLOW}docker-compose restart${NC}"
echo -e "   Run tests: ${YELLOW}./scripts/run-tests.sh${NC}"
echo ""
echo -e "${GREEN}🏆 Ready to WIN the Zurich Challenge!${NC}"
echo ""

# Open browser (optional)
if command -v open &> /dev/null; then
    echo -e "${BLUE}🌐 Opening application in browser...${NC}"
    open http://localhost:3000
elif command -v xdg-open &> /dev/null; then
    echo -e "${BLUE}🌐 Opening application in browser...${NC}"
    xdg-open http://localhost:3000
fi
