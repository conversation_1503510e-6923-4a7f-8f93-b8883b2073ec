#!/bin/bash

# 📊 Load Sample Data for Zurich Challenge Demo
# This script loads sample documents for testing and demo purposes

set -e

echo "📊 Loading Zurich Challenge Sample Data"
echo "======================================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Create sample data directories
echo -e "${BLUE}📁 Creating sample data structure...${NC}"
mkdir -p data/samples/{uc05,uc01,benchmarks}
mkdir -p data/samples/uc05/{fnol,emails,certificates,medical,incident}

# Copy Zurich sample documents if they exist
ZURICH_DATA_PATH="Zurich Challenge/05- Claims- Liability Decisions- Canada/Data"
if [ -d "$ZURICH_DATA_PATH" ]; then
    echo -e "${BLUE}📄 Copying Zurich sample documents...${NC}"
    
    # Find and copy sample documents
    find "$ZURICH_DATA_PATH" -name "*.txt" -type f | head -10 | while read file; do
        filename=$(basename "$file")
        case_dir=$(dirname "$file" | xargs basename)
        
        # Determine document type and copy to appropriate folder
        if [[ "$filename" == *"FNOL"* ]]; then
            cp "$file" "data/samples/uc05/fnol/${case_dir}_${filename}"
        elif [[ "$filename" == *"EXTERNAL"* ]] || [[ "$filename" == *".msg"* ]]; then
            cp "$file" "data/samples/uc05/emails/${case_dir}_${filename}"
        elif [[ "$filename" == *"Certificate"* ]]; then
            cp "$file" "data/samples/uc05/certificates/${case_dir}_${filename}"
        else
            cp "$file" "data/samples/uc05/incident/${case_dir}_${filename}"
        fi
    done
    
    echo -e "${GREEN}✅ Copied Zurich sample documents${NC}"
else
    echo -e "${YELLOW}⚠️  Zurich sample data not found, creating synthetic samples...${NC}"
    
    # Create synthetic sample documents for demo
    cat > "data/samples/uc05/fnol/sample_fnol.txt" << 'EOF'
FIRST NOTICE OF LOSS

Date of Loss: 2024-03-15
Time: 14:30
Location: 123 Main Street, Toronto, ON

Insured: John Smith
Policy Number: POL-123456789

Description of Incident:
Customer slipped and fell in grocery store aisle due to wet floor. 
Customer claims store failed to place warning signs after mopping.
Customer sustained injury to left ankle and was taken to hospital.

Witnesses: Store employee Sarah Johnson
Weather: Clear, dry conditions outside

Initial Assessment: Potential premises liability claim
Estimated Damages: TBD pending medical evaluation
EOF

    cat > "data/samples/uc05/emails/settlement_discussion.txt" << 'EOF'
From: <EMAIL>
To: <EMAIL>
Subject: Settlement Discussion - Claim GL1156110
Date: 2024-03-20

Dear Mr. Smith,

Following our investigation of the incident at the grocery store on March 15, 2024,
we have reviewed the available evidence including:
- Store surveillance footage
- Witness statements
- Medical records
- Incident report

Based on our analysis, we believe there may be shared responsibility for this incident.
The store did have cleaning protocols in place, however the warning signage was
not properly displayed at the time of your fall.

We would like to discuss a potential settlement to resolve this matter.
Please contact us to arrange a meeting.

Regards,
Claims Department
EOF

    cat > "data/samples/uc05/certificates/insurance_certificate.txt" << 'EOF'
CERTIFICATE OF LIABILITY INSURANCE

Policy Holder: ABC Grocery Store Ltd.
Policy Number: CGL-987654321
Effective Period: 2024-01-01 to 2024-12-31

Coverage Details:
General Liability: $2,000,000 per occurrence
                  $4,000,000 aggregate
Products Liability: $1,000,000 per occurrence
Premises Liability: $2,000,000 per occurrence

Deductible: $5,000

This certificate is issued for information purposes only and does not
constitute a contract between the issuing insurer and the certificate holder.
EOF

    echo -e "${GREEN}✅ Created synthetic sample documents${NC}"
fi

# Create benchmark test files
echo -e "${BLUE}📈 Creating benchmark test files...${NC}"

cat > "data/samples/benchmarks/ocr_accuracy_test.txt" << 'EOF'
OCR ACCURACY BENCHMARK TEST

This document contains various text elements to test OCR accuracy:

1. Standard printed text in Arial font
2. Bold and italic formatting variations
3. Numbers: 123456789, $1,234.56, 99.9%
4. Special characters: @#$%^&*()_+-=[]{}|;:,.<>?
5. Mixed case: CamelCase, UPPERCASE, lowercase
6. Common insurance terms: liability, deductible, premium, coverage
7. Canadian addresses: 123 Main St, Toronto, ON M5V 3A8
8. Dates in various formats: 2024-03-15, March 15, 2024, 15/03/2024

This text should be extracted with high accuracy by MiniCPM-o.
EOF

# Load sample data into the system
echo -e "${BLUE}🔄 Loading samples into the system...${NC}"

# Check if backend is running
if curl -s http://localhost:8000/health > /dev/null; then
    echo -e "${GREEN}✅ Backend is running, loading samples...${NC}"
    
    # Create a simple test upload
    # Note: This would typically use the API to upload files
    echo "Sample data prepared for manual upload through the UI"
else
    echo -e "${YELLOW}⚠️  Backend not running, samples prepared for later upload${NC}"
fi

# Create demo script
cat > "scripts/demo-samples.sh" << 'EOF'
#!/bin/bash
# Quick demo with sample files
echo "🎬 Starting Zurich Challenge Demo"
echo "Sample files available in data/samples/"
echo "1. UC05 Liability samples: data/samples/uc05/"
echo "2. Benchmark tests: data/samples/benchmarks/"
echo ""
echo "To test:"
echo "1. Open http://localhost:3000"
echo "2. Upload files from data/samples/uc05/"
echo "3. Watch real-time processing"
echo "4. Review liability assessment results"
EOF

chmod +x scripts/demo-samples.sh

echo ""
echo -e "${GREEN}🎉 Sample data loading complete!${NC}"
echo "======================================"
echo ""
echo -e "${BLUE}📁 Sample files created:${NC}"
echo "   UC05 Liability: data/samples/uc05/"
echo "   Benchmarks: data/samples/benchmarks/"
echo ""
echo -e "${BLUE}🎬 To run demo:${NC}"
echo "   ./scripts/demo-samples.sh"
echo ""
echo -e "${GREEN}Ready for Zurich Challenge demonstration!${NC}"
