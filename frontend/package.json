{"name": "zurich-challenge-frontend", "version": "1.0.0", "description": "🏆 Zurich Challenge - Winning Solution Frontend", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write ."}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "keywords": ["<PERSON>ich", "challenge", "insurance", "ai", "ocr", "liability", "react", "vite"], "author": "Zurich Challenge Team", "license": "MIT"}