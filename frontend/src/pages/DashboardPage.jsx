import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, FileText, Zap, Target, Award } from 'lucide-react';
import toast from 'react-hot-toast';

// Components
import SystemMetrics from '../components/SystemMetrics';
import UseCaseSelector from '../components/UseCaseSelector';
import FileUpload from '../components/FileUpload';

const DashboardPage = ({ selectedUseCase, systemStatus }) => {
  const navigate = useNavigate();
  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = async (files) => {
    if (!files || files.length === 0) {
      toast.error('Please select files to upload');
      return;
    }

    setIsUploading(true);
    
    try {
      // Simulate file upload and processing
      toast.loading('Uploading files...', { id: 'upload' });
      
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Generate a job ID
      const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      toast.success(`Files uploaded successfully! Starting processing...`, { id: 'upload' });
      
      // Navigate to processing page
      navigate(`/processing/${jobId}`);
      
    } catch (error) {
      console.error('Upload failed:', error);
      toast.error('Upload failed. Please try again.', { id: 'upload' });
    } finally {
      setIsUploading(false);
    }
  };

  const getUseCaseInfo = (useCase) => {
    const useCases = {
      'uc01': { name: 'Travel Claims', description: 'Automated travel insurance processing' },
      'uc02': { name: 'Motor Liability', description: 'Vehicle accident claim analysis' },
      'uc03': { name: 'Property Claims', description: 'Property damage assessment' },
      'uc04': { name: 'Fraud Detection', description: 'AI-powered fraud identification' },
      'uc05': { name: 'Liability Decisions', description: 'Fault assessment and liability analysis' }
    };
    return useCases[useCase] || useCases['uc05'];
  };

  const currentUseCase = getUseCaseInfo(selectedUseCase);

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg text-white p-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Award className="h-8 w-8 text-yellow-300" />
              <h1 className="text-3xl font-bold">Zurich Challenge</h1>
            </div>
            <p className="text-blue-100 text-lg mb-4">
              AI-Powered Insurance Claims Processing - Winning Solution
            </p>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span className="font-medium">{currentUseCase.name}</span>
              </div>
              <div className="text-blue-200">•</div>
              <div className="text-blue-200">{currentUseCase.description}</div>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="text-right">
              <div className="text-2xl font-bold">95%</div>
              <div className="text-blue-200">Accuracy Rate</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - File Upload */}
        <div className="lg:col-span-2 space-y-6">
          {/* File Upload Section */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Upload className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">
                Upload Documents
              </h2>
            </div>
            
            <FileUpload
              onUpload={handleFileUpload}
              isUploading={isUploading}
              selectedUseCase={selectedUseCase}
            />
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Quick Actions
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <FileText className="h-6 w-6 text-blue-600 mb-2" />
                <div className="font-medium">Sample Documents</div>
                <div className="text-sm text-gray-500">View example files</div>
              </button>
              
              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <Zap className="h-6 w-6 text-green-600 mb-2" />
                <div className="font-medium">Batch Processing</div>
                <div className="text-sm text-gray-500">Process multiple files</div>
              </button>
              
              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <Target className="h-6 w-6 text-purple-600 mb-2" />
                <div className="font-medium">API Access</div>
                <div className="text-sm text-gray-500">Integration guide</div>
              </button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Activity
            </h3>
            <div className="space-y-3">
              {[
                { id: 1, action: 'Processed liability claim', time: '2 minutes ago', status: 'completed' },
                { id: 2, action: 'Uploaded motor accident report', time: '15 minutes ago', status: 'completed' },
                { id: 3, action: 'Analyzed property damage', time: '1 hour ago', status: 'completed' },
                { id: 4, action: 'Fraud detection scan', time: '2 hours ago', status: 'completed' }
              ].map((activity) => (
                <div key={activity.id} className="flex items-center justify-between py-2">
                  <div>
                    <div className="font-medium text-gray-900">{activity.action}</div>
                    <div className="text-sm text-gray-500">{activity.time}</div>
                  </div>
                  <div className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    ✓ {activity.status}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - System Info */}
        <div className="space-y-6">
          {/* System Metrics */}
          <SystemMetrics systemStatus={systemStatus} />

          {/* Use Case Selector */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Use Case Selection
            </h3>
            <UseCaseSelector
              selectedUseCase={selectedUseCase}
              onUseCaseChange={() => {}} // Handled by parent
            />
          </div>

          {/* Performance Stats */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Performance Stats
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Processing Speed</span>
                <span className="font-semibold">2.3s avg</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Accuracy Rate</span>
                <span className="font-semibold text-green-600">95.2%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Documents Processed</span>
                <span className="font-semibold">1,247</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Success Rate</span>
                <span className="font-semibold text-green-600">98.7%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
