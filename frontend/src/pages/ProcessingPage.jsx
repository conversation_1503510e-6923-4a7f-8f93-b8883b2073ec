import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Clock, FileText, Zap, CheckCircle, AlertCircle, ArrowRight } from 'lucide-react';
import { useApi } from '../hooks/useApi';
import toast from 'react-hot-toast';

const ProcessingPage = ({ selectedUseCase }) => {
  const { jobId } = useParams();
  const navigate = useNavigate();
  const { get } = useApi();
  
  const [processingStatus, setProcessingStatus] = useState({
    status: 'processing',
    progress: 0,
    currentStep: 'Initializing...',
    steps: [],
    estimatedTime: 60
  });

  const [startTime] = useState(Date.now());
  const [elapsedTime, setElapsedTime] = useState(0);

  // Processing steps for different use cases
  const getProcessingSteps = (useCase) => {
    const baseSteps = [
      { id: 'upload', name: 'Document Upload', status: 'completed' },
      { id: 'validation', name: 'File Validation', status: 'completed' },
      { id: 'ocr', name: 'OCR Processing (MiniCPM-o)', status: 'processing' },
      { id: 'classification', name: 'Document Classification', status: 'pending' },
      { id: 'analysis', name: 'AI Analysis', status: 'pending' },
      { id: 'results', name: 'Generate Results', status: 'pending' }
    ];

    if (useCase === 'uc05') {
      return [
        ...baseSteps.slice(0, 4),
        { id: 'evidence', name: 'Evidence Extraction', status: 'pending' },
        { id: 'liability', name: 'Liability Assessment', status: 'pending' },
        { id: 'confidence', name: 'Confidence Scoring', status: 'pending' },
        ...baseSteps.slice(4)
      ];
    }

    return baseSteps;
  };

  useEffect(() => {
    // Initialize steps
    setProcessingStatus(prev => ({
      ...prev,
      steps: getProcessingSteps(selectedUseCase)
    }));

    // Simulate processing progress
    const interval = setInterval(() => {
      setElapsedTime(Date.now() - startTime);
      
      setProcessingStatus(prev => {
        const newProgress = Math.min(prev.progress + Math.random() * 15, 95);
        const currentStepIndex = Math.floor((newProgress / 100) * prev.steps.length);
        
        const updatedSteps = prev.steps.map((step, index) => {
          if (index < currentStepIndex) {
            return { ...step, status: 'completed' };
          } else if (index === currentStepIndex) {
            return { ...step, status: 'processing' };
          }
          return step;
        });

        const currentStep = updatedSteps[currentStepIndex]?.name || 'Finalizing...';

        // Complete processing when reaching 95%
        if (newProgress >= 95 && prev.status === 'processing') {
          setTimeout(() => {
            toast.success('Processing completed successfully!');
            navigate(`/results/${jobId}`);
          }, 2000);
          
          return {
            ...prev,
            progress: 100,
            status: 'completed',
            currentStep: 'Processing Complete',
            steps: updatedSteps.map(step => ({ ...step, status: 'completed' }))
          };
        }

        return {
          ...prev,
          progress: newProgress,
          currentStep,
          steps: updatedSteps
        };
      });
    }, 1500);

    return () => clearInterval(interval);
  }, [jobId, selectedUseCase, navigate, startTime]);

  const formatTime = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStepIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <div className="loading-spinner" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Processing Documents
        </h1>
        <p className="text-gray-600">
          AI-powered analysis in progress for {selectedUseCase.toUpperCase()}
        </p>
      </div>

      {/* Progress Overview */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Processing Progress
          </h2>
          <div className="text-sm text-gray-500">
            Job ID: {jobId}
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>{processingStatus.currentStep}</span>
            <span>{Math.round(processingStatus.progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${processingStatus.progress}%` }}
            />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <Clock className="h-6 w-6 text-blue-600 mx-auto mb-2" />
            <div className="text-lg font-semibold text-blue-900">
              {formatTime(elapsedTime)}
            </div>
            <div className="text-sm text-blue-600">Elapsed Time</div>
          </div>
          
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <Zap className="h-6 w-6 text-green-600 mx-auto mb-2" />
            <div className="text-lg font-semibold text-green-900">
              MiniCPM-o
            </div>
            <div className="text-sm text-green-600">OCR Engine</div>
          </div>
          
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <FileText className="h-6 w-6 text-purple-600 mx-auto mb-2" />
            <div className="text-lg font-semibold text-purple-900">
              {processingStatus.steps.filter(s => s.status === 'completed').length}
            </div>
            <div className="text-sm text-purple-600">Steps Completed</div>
          </div>
        </div>
      </div>

      {/* Processing Steps */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Processing Steps
        </h3>
        
        <div className="space-y-4">
          {processingStatus.steps.map((step, index) => (
            <div key={step.id} className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                {getStepIcon(step.status)}
              </div>
              
              <div className="flex-1">
                <div className={`font-medium ${
                  step.status === 'completed' ? 'text-green-700' :
                  step.status === 'processing' ? 'text-blue-700' :
                  step.status === 'error' ? 'text-red-700' :
                  'text-gray-500'
                }`}>
                  {step.name}
                </div>
                
                {step.status === 'processing' && (
                  <div className="text-sm text-blue-600 mt-1">
                    Currently processing...
                  </div>
                )}
                
                {step.status === 'completed' && (
                  <div className="text-sm text-green-600 mt-1">
                    ✓ Completed
                  </div>
                )}
              </div>
              
              {index < processingStatus.steps.length - 1 && (
                <ArrowRight className="h-4 w-4 text-gray-400" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Live Updates */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Live Updates</h4>
        <div className="space-y-2 text-sm text-gray-600">
          <div>• OCR processing with MiniCPM-o engine</div>
          <div>• Document classification in progress</div>
          <div>• AI analysis using GPT-4o</div>
          {selectedUseCase === 'uc05' && (
            <>
              <div>• Extracting liability evidence</div>
              <div>• Calculating fault percentages</div>
              <div>• Generating confidence scores</div>
            </>
          )}
        </div>
      </div>

      {/* Cancel Button */}
      <div className="text-center">
        <button
          onClick={() => navigate('/')}
          className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Return to Dashboard
        </button>
      </div>
    </div>
  );
};

export default ProcessingPage;
