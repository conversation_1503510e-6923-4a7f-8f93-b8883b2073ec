import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, Zap, Target, Award } from 'lucide-react';
import toast from 'react-hot-toast';
import { useApi } from '../hooks/useApi';
import UseCaseSelector from '../components/UseCaseSelector';
import SystemMetrics from '../components/SystemMetrics';

const Dashboard = ({ selectedUseCase, systemStatus }) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const navigate = useNavigate();
  const { post } = useApi();

  const onDrop = useCallback(async (acceptedFiles) => {
    if (acceptedFiles.length === 0) {
      toast.error('No valid files selected');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      acceptedFiles.forEach((file) => {
        formData.append('files', file);
      });
      formData.append('use_case', selectedUseCase);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await post('/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      toast.success(`${acceptedFiles.length} files uploaded successfully!`);
      
      // Navigate to processing page
      setTimeout(() => {
        navigate(`/processing/${response.data.job_id}`);
      }, 1000);

    } catch (error) {
      console.error('Upload failed:', error);
      toast.error('Upload failed. Please try again.');
    } finally {
      setUploading(false);
      setTimeout(() => setUploadProgress(0), 2000);
    }
  }, [selectedUseCase, post, navigate]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.png', '.jpg', '.jpeg', '.tiff'],
      'text/plain': ['.txt'],
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    disabled: uploading,
  });

  const useCaseInfo = {
    uc05: {
      title: 'Liability Decisions',
      description: 'AI-powered fault assessment for insurance claims',
      icon: Target,
      features: [
        'Document classification (FNOL, emails, certificates)',
        'Evidence extraction and analysis',
        'Fault percentage calculation',
        'Confidence scoring',
        'Canadian legal compliance'
      ]
    },
    uc01: {
      title: 'Travel Claims',
      description: 'Automated travel insurance claim processing',
      icon: FileText,
      features: [
        'Receipt validation',
        'Expense categorization',
        'Policy compliance check',
        'Fraud detection',
        'Automated approval workflow'
      ]
    }
  };

  const currentUseCase = useCaseInfo[selectedUseCase] || useCaseInfo.uc05;
  const IconComponent = currentUseCase.icon;

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-2">
          <Award className="h-8 w-8 text-yellow-500" />
          <h1 className="text-4xl font-bold text-gray-900">
            Zurich Challenge - Winning Solution
          </h1>
        </div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Advanced AI-powered document processing with state-of-the-art OCR and intelligent analysis
        </p>
      </div>

      {/* System Status */}
      <SystemMetrics systemStatus={systemStatus} />

      {/* Use Case Selection */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-semibold mb-4 flex items-center">
          <IconComponent className="h-6 w-6 mr-2 text-blue-600" />
          {currentUseCase.title}
        </h2>
        <p className="text-gray-600 mb-4">{currentUseCase.description}</p>
        
        <UseCaseSelector 
          selectedUseCase={selectedUseCase}
          onUseCaseChange={() => {}} // Handled by parent
        />

        <div className="mt-6">
          <h3 className="font-medium text-gray-900 mb-3">Key Features:</h3>
          <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {currentUseCase.features.map((feature, index) => (
              <li key={index} className="flex items-center text-sm text-gray-600">
                <Zap className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                {feature}
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Upload Section */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h2 className="text-xl font-semibold flex items-center">
            <Upload className="h-5 w-5 mr-2 text-blue-600" />
            Upload Documents
          </h2>
          <p className="text-gray-600 mt-1">
            Upload your insurance documents for AI-powered analysis
          </p>
        </div>

        <div className="p-6">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all
              ${isDragActive 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
              }
              ${uploading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            <input {...getInputProps()} />
            
            <div className="space-y-4">
              <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                <Upload className={`h-8 w-8 ${uploading ? 'text-gray-400' : 'text-gray-600'}`} />
              </div>
              
              {uploading ? (
                <div className="space-y-2">
                  <p className="text-lg font-medium text-gray-900">Uploading...</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                  <p className="text-sm text-gray-600">{uploadProgress}% complete</p>
                </div>
              ) : (
                <div>
                  <p className="text-lg font-medium text-gray-900">
                    {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
                  </p>
                  <p className="text-gray-600">
                    or <span className="text-blue-600 font-medium">browse</span> to select files
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    Supports PDF, Images (PNG, JPG, TIFF), and Text files up to 50MB
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Supported file types */}
          <div className="mt-4 flex flex-wrap gap-2">
            {['PDF', 'PNG', 'JPG', 'TIFF', 'TXT'].map((type) => (
              <span 
                key={type}
                className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
              >
                {type}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
          <div className="text-2xl font-bold text-blue-600">95%+</div>
          <div className="text-sm text-gray-600">OCR Accuracy</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
          <div className="text-2xl font-bold text-green-600">&lt;60s</div>
          <div className="text-sm text-gray-600">Processing Time</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
          <div className="text-2xl font-bold text-purple-600">18</div>
          <div className="text-sm text-gray-600">Use Cases Supported</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
          <div className="text-2xl font-bold text-orange-600">$0</div>
          <div className="text-sm text-gray-600">API Costs</div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
