import React from 'react';
import { Target, Plane, Car, Home, Shield, FileText, Users, TrendingUp, BarChart3, Settings, Zap, Globe, CreditCard, Building, Phone, Mail, Calendar, Database } from 'lucide-react';

const UseCaseSelector = ({ selectedUseCase, onUseCaseChange }) => {
  const useCases = [
    { 
      id: 'uc01', 
      name: 'Travel Claims', 
      icon: Plane, 
      description: 'Automated travel insurance claim processing and validation',
      features: ['Receipt validation', 'Expense categorization', 'Policy compliance']
    },
    { 
      id: 'uc02', 
      name: 'Motor Liability', 
      icon: Car, 
      description: 'Vehicle accident analysis and fault determination',
      features: ['Damage assessment', 'Fault analysis', 'Repair estimation']
    },
    { 
      id: 'uc03', 
      name: 'Property Claims', 
      icon: Home, 
      description: 'Property damage assessment and coverage analysis',
      features: ['Damage evaluation', 'Coverage verification', 'Cost estimation']
    },
    { 
      id: 'uc04', 
      name: 'Fraud Detection', 
      icon: Shield, 
      description: 'AI-powered fraud identification and risk assessment',
      features: ['Pattern recognition', 'Risk scoring', 'Alert generation']
    },
    { 
      id: 'uc05', 
      name: 'Liability Decisions', 
      icon: Target, 
      description: 'Comprehensive liability analysis and fault assessment',
      features: ['Evidence extraction', 'Fault calculation', 'Legal compliance']
    },
    { 
      id: 'uc06', 
      name: 'Document Classification', 
      icon: FileText, 
      description: 'Intelligent document categorization and processing',
      features: ['Auto-classification', 'Content extraction', 'Metadata tagging']
    },
    { 
      id: 'uc07', 
      name: 'Customer Analytics', 
      icon: Users, 
      description: 'Customer behavior analysis and insights',
      features: ['Behavior tracking', 'Segmentation', 'Predictive modeling']
    },
    { 
      id: 'uc08', 
      name: 'Risk Assessment', 
      icon: TrendingUp, 
      description: 'Comprehensive risk evaluation and scoring',
      features: ['Risk modeling', 'Score calculation', 'Trend analysis']
    },
    { 
      id: 'uc09', 
      name: 'Claims Analytics', 
      icon: BarChart3, 
      description: 'Advanced claims data analysis and reporting',
      features: ['Performance metrics', 'Trend analysis', 'Predictive insights']
    },
    { 
      id: 'uc10', 
      name: 'Policy Management', 
      icon: Settings, 
      description: 'Automated policy processing and management',
      features: ['Policy validation', 'Coverage analysis', 'Renewal processing']
    },
    { 
      id: 'uc11', 
      name: 'Workflow Automation', 
      icon: Zap, 
      description: 'Intelligent workflow orchestration and automation',
      features: ['Process automation', 'Task routing', 'SLA monitoring']
    },
    { 
      id: 'uc12', 
      name: 'Global Compliance', 
      icon: Globe, 
      description: 'Multi-jurisdiction compliance monitoring and reporting',
      features: ['Regulatory tracking', 'Compliance scoring', 'Report generation']
    },
    { 
      id: 'uc13', 
      name: 'Payment Processing', 
      icon: CreditCard, 
      description: 'Automated payment validation and processing',
      features: ['Payment verification', 'Fraud detection', 'Settlement automation']
    },
    { 
      id: 'uc14', 
      name: 'Corporate Claims', 
      icon: Building, 
      description: 'Enterprise-level claims processing and management',
      features: ['Bulk processing', 'Corporate policies', 'Advanced reporting']
    },
    { 
      id: 'uc15', 
      name: 'Communication Hub', 
      icon: Phone, 
      description: 'Centralized communication management and routing',
      features: ['Multi-channel support', 'Auto-routing', 'Response tracking']
    },
    { 
      id: 'uc16', 
      name: 'Email Processing', 
      icon: Mail, 
      description: 'Intelligent email classification and response automation',
      features: ['Email classification', 'Auto-response', 'Priority routing']
    },
    { 
      id: 'uc17', 
      name: 'Scheduling System', 
      icon: Calendar, 
      description: 'Automated appointment and task scheduling',
      features: ['Smart scheduling', 'Resource optimization', 'Conflict resolution']
    },
    { 
      id: 'uc18', 
      name: 'Data Integration', 
      icon: Database, 
      description: 'Seamless data integration and synchronization',
      features: ['Data mapping', 'Real-time sync', 'Quality validation']
    }
  ];

  const currentUseCase = useCases.find(uc => uc.id === selectedUseCase) || useCases[4]; // Default to UC05
  const IconComponent = currentUseCase.icon;

  return (
    <div className="space-y-4">
      {/* Current Selection Display */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="bg-blue-100 p-2 rounded-lg">
            <IconComponent className="h-6 w-6 text-blue-600" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-blue-900">{currentUseCase.name}</h3>
            <p className="text-blue-700 text-sm mt-1">{currentUseCase.description}</p>
            <div className="flex flex-wrap gap-2 mt-2">
              {currentUseCase.features.map((feature, index) => (
                <span 
                  key={index}
                  className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                >
                  {feature}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Use Case Grid Selector */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Use Case:
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
          {useCases.map((useCase) => {
            const IconComp = useCase.icon;
            const isSelected = selectedUseCase === useCase.id;
            
            return (
              <button
                key={useCase.id}
                onClick={() => onUseCaseChange(useCase.id)}
                className={`p-3 rounded-lg border text-left transition-all ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50 text-blue-900'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-2 mb-1">
                  <IconComp className={`h-4 w-4 ${
                    isSelected ? 'text-blue-600' : 'text-gray-400'
                  }`} />
                  <span className="font-medium text-sm">{useCase.name}</span>
                </div>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {useCase.description}
                </p>
              </button>
            );
          })}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-gray-900">18</div>
            <div className="text-xs text-gray-600">Total Use Cases</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-600">5</div>
            <div className="text-xs text-gray-600">Implemented</div>
          </div>
          <div>
            <div className="text-lg font-bold text-blue-600">95%</div>
            <div className="text-xs text-gray-600">Accuracy</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UseCaseSelector;
