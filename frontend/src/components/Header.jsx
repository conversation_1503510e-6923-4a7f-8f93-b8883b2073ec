import React from 'react';
import { Menu, Award, Activity, AlertCircle } from 'lucide-react';

const Header = ({ selectedUseCase, onUseCaseChange, systemStatus, onMenuClick }) => {
  const getStatusIcon = () => {
    switch (systemStatus) {
      case 'healthy':
        return <Activity className="h-4 w-4 text-green-500" />;
      case 'loading':
        return <div className="loading-spinner" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (systemStatus) {
      case 'healthy':
        return 'System Healthy';
      case 'loading':
        return 'Connecting...';
      case 'error':
        return 'System Error';
      default:
        return 'Unknown';
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side */}
          <div className="flex items-center space-x-4">
            <button
              onClick={onMenuClick}
              className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <Menu className="h-6 w-6" />
            </button>
            
            <div className="flex items-center space-x-2">
              <Award className="h-8 w-8 text-yellow-500" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Zurich Challenge
                </h1>
                <p className="text-sm text-gray-500">Winning Solution</p>
              </div>
            </div>
          </div>

          {/* Center - Use Case Selector */}
          <div className="hidden md:flex items-center space-x-4">
            <select
              value={selectedUseCase}
              onChange={(e) => onUseCaseChange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="uc05">UC05: Liability Decisions</option>
              <option value="uc01">UC01: Travel Claims</option>
              <option value="uc02">UC02: Motor Liability</option>
              <option value="uc03">UC03: Property Claims</option>
              <option value="uc04">UC04: Fraud Detection</option>
            </select>
          </div>

          {/* Right side - System Status */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className="text-sm text-gray-600 hidden sm:inline">
                {getStatusText()}
              </span>
            </div>
            
            <div className="text-sm text-gray-500">
              <span className="font-medium">v1.0.0</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
