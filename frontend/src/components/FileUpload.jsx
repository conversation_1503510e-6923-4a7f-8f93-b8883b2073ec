import React, { useState, useRef } from 'react';
import { Upload, File, X, CheckCircle, AlertCircle } from 'lucide-react';

const FileUpload = ({ onUpload, isUploading, selectedUseCase }) => {
  const [dragActive, setDragActive] = useState(false);
  const [files, setFiles] = useState([]);
  const fileInputRef = useRef(null);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = (fileList) => {
    const newFiles = Array.from(fileList).map(file => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'ready'
    }));
    
    setFiles(prev => [...prev, ...newFiles]);
  };

  const removeFile = (fileId) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type) => {
    if (type.includes('image')) return '🖼️';
    if (type.includes('pdf')) return '📄';
    if (type.includes('word')) return '📝';
    if (type.includes('excel') || type.includes('spreadsheet')) return '📊';
    if (type.includes('text') || type.includes('plain')) return '📝';
    return '📄';
  };

  const handleUpload = () => {
    if (files.length === 0) return;
    onUpload(files.map(f => f.file));
  };

  const acceptedTypes = {
    'uc01': '.pdf,.jpg,.jpeg,.png,.doc,.docx,.txt,.msg,text/plain,application/pdf,image/*', // Travel claims
    'uc02': '.pdf,.jpg,.jpeg,.png,.doc,.docx,.txt,.msg,text/plain,application/pdf,image/*', // Motor liability
    'uc03': '.pdf,.jpg,.jpeg,.png,.doc,.docx,.txt,.msg,text/plain,application/pdf,image/*', // Property claims
    'uc04': '.pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.txt,.msg,text/plain,application/pdf,image/*', // Fraud detection
    'uc05': '.pdf,.jpg,.jpeg,.png,.doc,.docx,.txt,.msg,.tiff,.tif,text/plain,application/pdf,image/*' // Liability decisions
  };

  return (
    <div className="space-y-4">
      {/* Upload Zone */}
      <div
        className={`file-upload-zone ${dragActive ? 'drag-active' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleChange}
          {...(selectedUseCase !== 'uc05' && {
            accept: acceptedTypes[selectedUseCase] || '.pdf,.jpg,.jpeg,.png,.doc,.docx,.txt,text/plain,application/pdf,image/*'
          })}
          className="hidden"
        />
        
        <div className="text-center">
          <Upload className={`mx-auto h-12 w-12 ${
            dragActive ? 'text-blue-500' : 'text-gray-400'
          }`} />
          <div className="mt-4">
            <p className="text-lg font-medium text-gray-900">
              {dragActive ? 'Drop files here' : 'Upload Documents'}
            </p>
            <p className="text-gray-500 mt-1">
              Drag and drop files here, or click to select
            </p>
            <p className="text-sm text-gray-400 mt-2">
              Supported: PDF, Images, Word documents, Text files
              {selectedUseCase === 'uc04' && ', Excel files'}
            </p>
          </div>
        </div>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900">Selected Files ({files.length})</h4>
          <div className="max-h-48 overflow-y-auto space-y-2">
            {files.map((fileItem) => (
              <div
                key={fileItem.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
              >
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getFileIcon(fileItem.type)}</span>
                  <div>
                    <div className="font-medium text-gray-900 truncate max-w-xs">
                      {fileItem.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatFileSize(fileItem.size)}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {fileItem.status === 'ready' && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                  {fileItem.status === 'error' && (
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  )}
                  <button
                    onClick={() => removeFile(fileItem.id)}
                    className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Button */}
      {files.length > 0 && (
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-gray-600">
            {files.length} file{files.length !== 1 ? 's' : ''} ready for processing
          </div>
          <div className="space-x-2">
            <button
              onClick={() => setFiles([])}
              disabled={isUploading}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Clear All
            </button>
            <button
              onClick={handleUpload}
              disabled={isUploading || files.length === 0}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                isUploading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isUploading ? (
                <div className="flex items-center space-x-2">
                  <div className="loading-spinner" />
                  <span>Uploading...</span>
                </div>
              ) : (
                `Process ${files.length} File${files.length !== 1 ? 's' : ''}`
              )}
            </button>
          </div>
        </div>
      )}

      {/* Use Case Specific Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h5 className="font-medium text-blue-900 mb-2">
          {selectedUseCase.toUpperCase()} - Document Requirements
        </h5>
        <div className="text-sm text-blue-800">
          {selectedUseCase === 'uc01' && (
            <ul className="list-disc list-inside space-y-1">
              <li>Travel receipts and invoices</li>
              <li>Medical reports (if applicable)</li>
              <li>Travel insurance policy documents</li>
            </ul>
          )}
          {selectedUseCase === 'uc02' && (
            <ul className="list-disc list-inside space-y-1">
              <li>Accident reports and photos</li>
              <li>Vehicle damage assessments</li>
              <li>Police reports (if available)</li>
            </ul>
          )}
          {selectedUseCase === 'uc03' && (
            <ul className="list-disc list-inside space-y-1">
              <li>Property damage photos</li>
              <li>Repair estimates and invoices</li>
              <li>Property insurance documents</li>
            </ul>
          )}
          {selectedUseCase === 'uc04' && (
            <ul className="list-disc list-inside space-y-1">
              <li>Claim documents and supporting evidence</li>
              <li>Financial records and statements</li>
              <li>Identity verification documents</li>
            </ul>
          )}
          {selectedUseCase === 'uc05' && (
            <ul className="list-disc list-inside space-y-1">
              <li>Incident reports and witness statements</li>
              <li>Photos and video evidence</li>
              <li>Legal documents and correspondence</li>
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileUpload;
