import React, { useState, useEffect } from 'react';
import { Activity, Cpu, HardDrive, Zap, Clock } from 'lucide-react';

const SystemMetrics = ({ systemStatus }) => {
  const [metrics, setMetrics] = useState({
    cpu: 0,
    memory: 0,
    disk: 0,
    uptime: 0,
    requests: 0
  });

  useEffect(() => {
    // Simulate real-time metrics for demo
    const interval = setInterval(() => {
      setMetrics({
        cpu: Math.random() * 30 + 10, // 10-40%
        memory: Math.random() * 20 + 40, // 40-60%
        disk: Math.random() * 10 + 20, // 20-30%
        uptime: Date.now() / 1000, // Current timestamp
        requests: Math.floor(Math.random() * 100) + 50 // 50-150 req/min
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const formatUptime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-100';
      case 'loading':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
          <Activity className="h-5 w-5 mr-2 text-blue-600" />
          System Status
        </h2>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(systemStatus)}`}>
          {systemStatus === 'healthy' ? '🟢 Healthy' : 
           systemStatus === 'loading' ? '🟡 Starting' : 
           systemStatus === 'error' ? '🔴 Error' : '⚪ Unknown'}
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        {/* CPU Usage */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-2">
            <Cpu className="h-5 w-5 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {metrics.cpu.toFixed(1)}%
          </div>
          <div className="text-sm text-gray-500">CPU</div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${metrics.cpu}%` }}
            />
          </div>
        </div>

        {/* Memory Usage */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-2">
            <HardDrive className="h-5 w-5 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {metrics.memory.toFixed(1)}%
          </div>
          <div className="text-sm text-gray-500">Memory</div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div 
              className="bg-green-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${metrics.memory}%` }}
            />
          </div>
        </div>

        {/* Disk Usage */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-2">
            <HardDrive className="h-5 w-5 text-purple-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {metrics.disk.toFixed(1)}%
          </div>
          <div className="text-sm text-gray-500">Disk</div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div 
              className="bg-purple-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${metrics.disk}%` }}
            />
          </div>
        </div>

        {/* Requests per minute */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-2">
            <Zap className="h-5 w-5 text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {metrics.requests}
          </div>
          <div className="text-sm text-gray-500">Req/min</div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div 
              className="bg-orange-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${Math.min(metrics.requests, 100)}%` }}
            />
          </div>
        </div>

        {/* Uptime */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-2">
            <Clock className="h-5 w-5 text-indigo-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {formatUptime(metrics.uptime)}
          </div>
          <div className="text-sm text-gray-500">Uptime</div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div className="bg-indigo-600 h-2 rounded-full w-full" />
          </div>
        </div>
      </div>

      {/* Services Status */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Services</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[
            { name: 'API', status: 'healthy' },
            { name: 'OCR Engine', status: 'healthy' },
            { name: 'Database', status: 'healthy' },
            { name: 'Workflows', status: 'healthy' }
          ].map((service) => (
            <div key={service.name} className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                service.status === 'healthy' ? 'bg-green-500' : 'bg-red-500'
              }`} />
              <span className="text-sm text-gray-600">{service.name}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SystemMetrics;
