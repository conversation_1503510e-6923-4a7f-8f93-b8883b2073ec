import React from 'react';
import { X, Target, Plane, Car, Home, Shield, FileText, Users, TrendingUp, BarChart3, Settings } from 'lucide-react';

const Sidebar = ({ isOpen, onClose, selectedUseCase, onUseCaseChange }) => {
  const useCases = [
    { id: 'uc01', name: 'Travel Claims', icon: Plane, description: 'Automated travel insurance processing' },
    { id: 'uc02', name: 'Motor Liability', icon: Car, description: 'Vehicle accident claim analysis' },
    { id: 'uc03', name: 'Property Claims', icon: Home, description: 'Property damage assessment' },
    { id: 'uc04', name: 'Fraud Detection', icon: Shield, description: 'AI-powered fraud identification' },
    { id: 'uc05', name: 'Liability Decisions', icon: Target, description: 'Fault assessment and liability analysis' },
  ];

  const menuItems = [
    { id: 'documents', name: 'Documents', icon: FileText },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'reports', name: 'Reports', icon: TrendingUp },
    { id: 'users', name: 'Users', icon: Users },
    { id: 'settings', name: 'Setting<PERSON>', icon: Settings },
  ];

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className="fixed left-0 top-0 h-full w-64 bg-white shadow-lg z-50 transform transition-transform duration-300 ease-in-out">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Navigation</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Use Cases */}
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
            Use Cases
          </h3>
          <div className="space-y-2">
            {useCases.map((useCase) => {
              const IconComponent = useCase.icon;
              const isSelected = selectedUseCase === useCase.id;
              
              return (
                <button
                  key={useCase.id}
                  onClick={() => {
                    onUseCaseChange(useCase.id);
                    onClose();
                  }}
                  className={`w-full flex items-start p-3 rounded-lg text-left transition-colors ${
                    isSelected
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <IconComponent className={`h-5 w-5 mt-0.5 mr-3 flex-shrink-0 ${
                    isSelected ? 'text-blue-600' : 'text-gray-400'
                  }`} />
                  <div>
                    <div className="font-medium">{useCase.name}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      {useCase.description}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Menu Items */}
        <div className="p-4 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
            Menu
          </h3>
          <div className="space-y-1">
            {menuItems.map((item) => {
              const IconComponent = item.icon;
              
              return (
                <button
                  key={item.id}
                  className="w-full flex items-center p-2 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  <IconComponent className="h-5 w-5 mr-3 text-gray-400" />
                  <span>{item.name}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-center">
            <div className="text-sm font-medium text-gray-900">Zurich Challenge</div>
            <div className="text-xs text-gray-500">Winning Solution v1.0</div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
