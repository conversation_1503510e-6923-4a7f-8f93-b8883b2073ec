@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Zurich Challenge */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: rgba(17, 24, 39, 1);
  background-color: rgba(249, 250, 251, 1);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
}

/* Custom component styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

/* Loading animations */
.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Progress bar */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-fill {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out;
}

/* File upload styles */
.file-upload-zone {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer transition-all duration-200;
}

.file-upload-zone:hover {
  @apply border-gray-400 bg-gray-50;
}

.file-upload-zone.drag-active {
  @apply border-blue-500 bg-blue-50;
}

/* Status indicators */
.status-healthy {
  @apply text-green-600 bg-green-100;
}

.status-warning {
  @apply text-yellow-600 bg-yellow-100;
}

.status-error {
  @apply text-red-600 bg-red-100;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
}
