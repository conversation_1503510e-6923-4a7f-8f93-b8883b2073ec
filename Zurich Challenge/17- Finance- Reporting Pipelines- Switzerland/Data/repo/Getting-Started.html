<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Contact Person</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1>Table of Contents</h1>
<ul>
<li><a href="#contact-person">Contact Person</a></li>
<li><a href="#objective">Objective</a></li>
<li><a href="#data-package">Data Package</a>
<ul>
<li><a href="#schemas">Schemas</a></li>
</ul>
</li>
<li><a href="#next-steps">Next steps</a>
<ul>
<li><a href="#option-1---open-source-setup">Option 1 - Open Source Setup</a></li>
<li><a href="#option-2---bring-your-own-stack">Option 2 - Bring your own stack</a></li>
</ul>
</li>
<li><a href="#evaluation-criteria">Evaluation criteria</a>
<ul>
<li><a href="#general-notes">General Notes</a></li>
</ul>
</li>
<li><a href="#qa">Q&amp;A</a></li>
</ul>
<h1 id="contact-person">Contact Person</h1>
<p>Subject matter expert for this use case:
Tomasz Skorkowski</p>
<h1 id="objective">Objective</h1>
<p>Use agents to operate and construct reporting pipelines. Show how agents can be deployed in orchestration layer to help with data flow. Create better experience for both owner and consumer. Build a few reporting pipelines, add inter dependencies, add validation and summary. Unleash your agents and show us how they can help us to build a smooth and resilient reporting pipeline. ​</p>
<p>Key features:</p>
<ul>
<li>Operate ETL (Extract-Transform-Load) pipeline with minimal human intervention​</li>
<li>Setup reporting process based on requirements ​</li>
<li>Allow management to request specific information​</li>
<li>Create pipeline on request</li>
</ul>
<p>Data provided for this challenge is either randomly generated or publicly available. The goal is not a numeric result but rather operational excellence.</p>
<p><a id="features-details"></a>
Features we are looking for in more detail:</p>
<ol>
<li>Core
<ol>
<li>Provide status information on the reporting and where to find specific outputs with some highlights.</li>
<li>Be able to query details about reporting process. Use pipelines as a documentation.</li>
<li>Consume PDF input with reporting requirements and set up/flag elements that have to be executed <a id="rep-req"></a></li>
<li>Execute pipelines</li>
<li>Meta data tracking, flag outliers</li>
<li>Clearly show Agent's actions and interventions</li>
</ol>
</li>
<li>Really nice
<ol>
<li>Setup new reporting pipelines</li>
<li>Show that agents can fix issues with inputs
<ol>
<li>Type errors</li>
<li>Changes to the naming convention</li>
<li>Missing data</li>
</ol>
</li>
<li>Prepare audit trail</li>
</ol>
</li>
<li>Extras
<ol>
<li>Anything that you think could bring value</li>
</ol>
</li>
</ol>
<h1 id="data-package">Data Package</h1>
<p>Data provided for this challenge:</p>
<table>
<thead>
<tr>
<th style="text-align:left">File/Folder</th>
<th style="text-align:right">Description</th>
<th style="text-align:right">Format</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">Interest_Rates</td>
<td style="text-align:right">Folder contains sample information about interest rates at specific time points. Public information, more can be downloaded <a href="https://www.eiopa.europa.eu/tools-and-data/risk-free-interest-rate-term-structures_en">here</a></td>
<td style="text-align:right">.xlsx</td>
</tr>
<tr>
<td style="text-align:left">2024_Q4_CLOSE_INSTRUCTIONS</td>
<td style="text-align:right">Extract from the latest, full-year reporting <a href="#rep-req">requirements</a>. treat it as an input that agents could use to set up relevant pipelines. Also source of meta data.</td>
<td style="text-align:right">.pdf</td>
</tr>
<tr>
<td style="text-align:left">mock_mpf_data_1</td>
<td style="text-align:right">Dummy policy information, years 2021-2024. Schema information will be provided below</td>
<td style="text-align:right">.csv</td>
</tr>
<tr>
<td style="text-align:left">mock_mpf_data_2</td>
<td style="text-align:right">Dummy policy information, years 2021-2024. Schema information will be provided below</td>
<td style="text-align:right">.xlsx</td>
</tr>
<tr>
<td style="text-align:left">mock_pro_data</td>
<td style="text-align:right">Dummy extract from calculation engine, years 2021-2024. Schema information will be provided below</td>
<td style="text-align:right">.parquet</td>
</tr>
<tr>
<td style="text-align:left">repo</td>
<td style="text-align:right">Sample pipelines based on the above data build with open source packages - optional. You can go with your own tools and build pipelines as well</td>
<td style="text-align:right">-</td>
</tr>
</tbody>
</table>
<h2 id="schemas">Schemas</h2>
<p>Dummy policy information:</p>
<pre><code class="language-python">        schema={
            <span class="hljs-string">&#x27;id&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Unique ID</span>
            <span class="hljs-string">&#x27;year&#x27;</span>: <span class="hljs-built_in">int</span>, <span class="hljs-comment"># Information as at end of year</span>
            <span class="hljs-string">&#x27;portfolio&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Letter representing a portfolio/product type</span>
            <span class="hljs-string">&#x27;premium&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Premium paid by a specific contract           </span>
            <span class="hljs-string">&#x27;contract_duration&#x27;</span>: <span class="hljs-built_in">int</span>, <span class="hljs-comment"># Length of a contract</span>
            <span class="hljs-string">&#x27;INIT_MEM_IF&#x27;</span>: <span class="hljs-built_in">int</span>, <span class="hljs-comment"># Number of insured under the contract</span>
            }
</code></pre>
<p>Dummy extract from calculation engine:</p>
<pre><code class="language-python">        schema={
            <span class="hljs-string">&#x27;run&#x27;</span>:<span class="hljs-built_in">str</span>, <span class="hljs-comment"># Calculation name</span>
            <span class="hljs-string">&#x27;cashflow_year&#x27;</span>:<span class="hljs-built_in">int</span>, <span class="hljs-comment"># Projection start year, different cashflow year mean calculation done at a different point in time</span>
            <span class="hljs-string">&#x27;cashflow_month&#x27;</span>:<span class="hljs-built_in">int</span>, <span class="hljs-comment"># Month of a cashflow</span>
            <span class="hljs-string">&#x27;prophet_variable&#x27;</span>:<span class="hljs-built_in">str</span>, <span class="hljs-comment"># Variable from calculation engine</span>
            <span class="hljs-string">&#x27;cashflow_amount&#x27;</span>:<span class="hljs-built_in">float</span>, <span class="hljs-comment"># Cashflow value</span>
            <span class="hljs-string">&#x27;cashflow_present_value_current_rate&#x27;</span>:<span class="hljs-built_in">float</span>, <span class="hljs-comment"># Discounted value of future cashflows to cashflow_year and cashflow_month</span>
            <span class="hljs-string">&#x27;cashflow_present_value_undiscounted&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Undiscounted (sum) of future cashflows to cashflow_year year and cashflow_month</span>
            <span class="hljs-string">&#x27;category&#x27;</span>: <span class="hljs-built_in">str</span> <span class="hljs-comment"># Descriptive category, premiums|outgoes|asset value|expenses etc.</span>
        }
</code></pre>
<h1 id="next-steps">Next steps</h1>
<h2 id="option-1---open-source-setup">Option 1 - Open Source Setup</h2>
<ol>
<li>Go to repo folder</li>
<li>Install uv - <a href="https://docs.astral.sh/uv/getting-started/installation/">installation guide</a></li>
<li>Run:<pre><code class="language-powershell">uv sync
</code></pre>
</li>
<li>Install dbt dependencies:<pre><code class="language-powershell"><span class="hljs-built_in">cd</span> dbt
uv run dbt deps
</code></pre>
</li>
</ol>
<p>You have just set up a small pipeline exemplar based on the open source tech stack:</p>
<ul>
<li><a href="https://duckdb.org/docs/stable/">DuckDB</a></li>
<li><a href="https://docs.getdbt.com/docs/core/about-core-setup">dbt Core</a></li>
<li><a href="https://docs.dagster.io/">Dagster</a></li>
</ul>
<p>Check it out by running:</p>
<p><strong>dbt</strong> - transformation workflow</p>
<pre><code class="language-powershell"><span class="hljs-built_in">cd</span> dbt
uv run dbt docs generate
uv run dbt docs serve
</code></pre>
<p><strong>Dagster</strong> - orchestrator</p>
<p>Go back to the main repo folder</p>
<pre><code class="language-powershell">uv run dagster dev
</code></pre>
<p>If things went well you should see something like this:</p>
<img src="file:///c:\Github\pipelines_challenge\images\dagster_ui.jpeg" alt="Dagster UI" width="1000" height="600">
<p>Now you have an access to data along with small pipelines and environment to experiment with. This is your starting point, hack away!</p>
<h2 id="option-2---bring-your-own-stack">Option 2 - Bring your own stack</h2>
<p>Take provided inputs and create a few reporting pipelines.</p>
<p>What package/software to use for that? Anything that we would be able to experiment with and you'll be able to show us. If this would incur additional costs for the pilot please let us know. You can also use some parts of exemplar from open source section.</p>
<p>Build agents on top and show us what is possible!</p>
<h1 id="evaluation-criteria">Evaluation criteria</h1>
<p>Your submission will be evaluated against <a href="#features-details">feature list</a>. Features are split between core and extra category. Core features have priority.</p>
<p>Please bare in mind that we will have limited time to review your submissions. Make sure to prepare it in a way
that will make it easy for us to do so. If your solution is the best but we can't see it it will not be selected.</p>
<h3 id="general-notes">General Notes</h3>
<p>Our expectation would be to see something between 3 and 10 reports with interdependencies. Again the numbers are not important so don't really worry if they mean anything.</p>
<p>Think if number of reports you're showing is enough to showcase the power of your agents. When preparing a submission please do not forget to include information about limitations and potential bottlenecks.</p>
<ul>
<li>Would it be as efficient with 100 reports as it is with 5, what is the scaling limitation.</li>
<li>What is the expected running cost</li>
<li>How would you scope a pilot project, what milestones and challenges do you see?</li>
<li>Please submit detailed reports summarizing technical requirements for successful adoption.</li>
</ul>
<h1 id="qa">Q&amp;A</h1>
<p>This section will be updated as we go along.</p>
<p>Q: I would need more data to showcase something, <strong>can I create it myself</strong>?</p>
<p>A: <strong>Absolutely</strong>, be mindful that we will need to test it afterwards. What it means is we could ask for a script to generate test data. For simplicity and ease of use lets make sure that any extra data will be generated with <strong>python</strong> and is compatible with <strong>version 3.10</strong></p>
<p>Q: I need <strong>more metadata</strong></p>
<p>A: <strong>Feel free to create it</strong>. One of the goals would be to understand what and how much information we need to give to the agents such that they can differentiate between specific reports.</p>
<p>Q: Can I modify/extend/add components to the exemplar?</p>
<p>A: Again, <strong>absolutely</strong>. You'd like to add a dashboard, extra report, functionality - please do so. Exemplar is just a way to get you started faster but it should not limit you. Feel free to change it in any way that you see fit.</p>
<p>Q: I need to get in touch with Subject Matter Expert</p>
<p>A: Make sure that your question is not something that can be answered via Teams Channel. If you still need a 1-1 session request a meeting vai teams. Be specific, be prepared, add detailed description of the problem and why you need in person meeting in your message.</p>

            
            
        </body>
        </html>