import dagster as dg
from dagster_dbt import dbt_assets, DbtCliResource
from orchestration.partitions import year_partitions

from orchestration.project import dbt_project
import json

INCREMENTAL_SELECTOR = "config.materialized:incremental"


@dbt_assets(manifest=dbt_project.manifest_path, exclude=INCREMENTAL_SELECTOR)
def dbt_reporting(context: dg.AssetExecutionContext, dbt: DbtCliResource):
    yield from dbt.cli(["build"], context=context).stream()


@dbt_assets(
    manifest=dbt_project.manifest_path,
    select=INCREMENTAL_SELECTOR,
    partitions_def=year_partitions,
)
def incremental_dbt_models(context: dg.AssetExecutionContext, dbt: DbtCliResource):
    dbt_year = {"year": context.partition_key}

    yield from dbt.cli(
        ["build", "--vars", json.dumps(dbt_year)], context=context
    ).stream()
