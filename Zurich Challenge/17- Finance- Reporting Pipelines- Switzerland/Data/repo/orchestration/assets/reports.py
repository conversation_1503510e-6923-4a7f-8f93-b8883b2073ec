import dagster as dg
from dagster_duckdb import DuckDBResource
from orchestration.partitions import year_partitions
import polars as pl
from dagster import TableRecord
from typing import Union, Sequence
from polars import selectors as cs
import yaml


def execute_duck_sql(
    sql: str,
    duckdb: DuckDBResource,
    to_register: Union[pl.DataFrame, None] = None,
    register_name: Union[str, None] = None,
) -> pl.DataFrame:
    with duckdb.get_connection() as conn:
        if to_register is not None and register_name is not None:
            conn.register(register_name, to_register)
        result = conn.execute(sql)
        data = result.fetch_df()

    return pl.DataFrame(data)


def aoc_generator(extract: pl.DataFrame, runs: Sequence[str]) -> pl.DataFrame:
    """
    Generates the AOC report based on the provided extract and runs.

    Args:
        extract (pl.DataFrame): The input data frame containing the necessary data.
        runs (Sequence[str]): The list of runs to include in the report.

    Returns:
        pl.DataFrame: The generated AOC report.
    """
    assert len(runs) >= 2, "AoC report has to have at least two runs"

    run_order = {run: i for i, run in enumerate(runs)}

    extract_sorted = (
        extract.with_columns(pl.col("run").replace(run_order).alias("sort_order")).sort(
            "sort_order"
        )
    )

    cols = cs.numeric().exclude(["cashflow_year", "sort_order"])
    deltas_aoc = (
        extract_sorted.pivot(
            on="category",
            values="cashflow_present_value_current_rate",
        )
        .with_columns(
            cols.diff(),
        )
        .fill_null(0)
    )

    output = deltas_aoc

    return output


@dg.asset(deps=["pro", "mpf"], partitions_def=year_partitions, group_name="Reporting")
def impact_assessment(context: dg.AssetExecutionContext, duck: DuckDBResource):

    year = context.partition_key

    sql_mpf = f"""
        SELECT 
        id
        , contract_duration
        , INIT_MEM_IF
        , portfolio
        , year
        FROM mpf
        WHERE year = {year}
        """

    with duck.get_connection() as conn:
        result = conn.execute(sql_mpf)
        data = pl.DataFrame(result.fetch_df())

    model_point_summary = data.group_by(
        pl.col("contract_duration", "portfolio"),
    ).agg(
        pl.col("year").first(),
        INIT_MEM_IF=pl.col("INIT_MEM_IF").sum(),
    )

    output_sql = f"""
        CREATE or replace TABLE mpf_summary AS SELECT * FROM model_point_summary LIMIT 0;
        
        DELETE from mpf_summary where year = '{year}';
    
        INSERT INTO mpf_summary SELECT * FROM model_point_summary;
    """

    with duck.get_connection() as conn:
        conn.register("model_point_summary", model_point_summary)
        conn.execute(output_sql)

        context.add_output_metadata(
            {
                "preview": dg.MetadataValue.table(
                    records=[
                        TableRecord(row)
                        for row in model_point_summary.group_by(
                            "contract_duration", "portfolio"
                        )
                        .agg(pl.col("INIT_MEM_IF").sum())
                        .to_dicts()
                    ],
                ),
                "row_count": model_point_summary.shape[0],  # Add row count as metadata
                "column_count": model_point_summary.shape[
                    1
                ],  # Add column count as metadata
                "init_mem_if_sum": model_point_summary.select("INIT_MEM_IF")
                .sum()
                .to_numpy()
                .item(),
            }
        )


@dg.asset(deps=["pro"], partitions_def=year_partitions, group_name="Reporting")
def risk_based_capital(context: dg.AssetExecutionContext, duck: DuckDBResource):
    """
    Under Zurich Group methodology, Risk Based Capital (RBC) represents the amount of capital required to ensure that the particular entity (Group or standalone unit) remains solvent (on an economic basis) with a defined level of confidence over a one-year time horizon.

    RBC is calculated for each Reporting Unit, Group diversified basis and Standalone basis.

    The Standalone basis represents the calculation of RBC for the RU on the assumption that it is a standalone entity. On the Group diversification basis, additional diversification benefits arise from being part of a larger, diversified Group – i.e.
    RBC for ZIG at Group level < Sum of Standalone RBC across all RUs

    The additional Group diversification benefits are allocated back to RUs to derive the Group diversified RBC for individual RUs. Therefore
    RBC for RU on Group diversified basis < RBC for RU on Standalone basis

    The data collected as part of RBC submission is used to inform both the Z-ECM internal capital metric and Group SST regulatory risk capital metric.

    This report is a SST RBC impacts report.

    """
    pro_results = [
        "SST_EoP_Monthly",
        "SST_Mort_Up",
        "SST_Pandemic",
        "SST_Annuities_Mort_Decrease",
        "SST_Long_Trend_Ann_Mort_Improvement",
        "SST_Morbidity_Up_Rec_Dn",
        "SST_Maint_Exp_Up",
        "SST_Exp_Infl_Up",
        "SST_Mass_Lapse",
        "SST_Lapse_Up",
        "SST_Rec_Dn",
    ]

    year = context.partition_key

    run_names_str = ",".join(f"'{name}'" for name in pro_results) or ""

    input_sql = f"""
        SELECT *
        FROM pro
        where run in ({run_names_str})
         and cashflow_year = {year}
        
    """

    extract = execute_duck_sql(input_sql, duck)

    output_base = extract.filter(
        pl.col("run") == "SST_EoP_Monthly",
    )

    output_stressed = extract.filter(
        pl.col("run") != "SST_EoP_Monthly",
    )

    cols = cs.numeric().exclude("cashflow_year", "cashflow_month")

    base_row = output_base.select(cols).row(0)
    output = output_stressed.with_columns(
        [
            (base_row[i] - output_stressed[col]).alias(col)
            for i, col in enumerate(output_stressed.select(cols).columns)
        ]
    )

    output = pl.concat([output_base, output], how="vertical")
    context.add_output_metadata(
        {
            "preview": dg.MetadataValue.table(
                records=[TableRecord(row) for row in output.to_dicts()],
            ),
            "row_count": output.shape[0],
            "column_count": output.shape[1],
        }
    )


def build_aoc_reports(name: str, runs: Sequence[str], description: str):
    @dg.asset(
        name=name,
        deps=["pro"],
        partitions_def=year_partitions,
        group_name="Reporting",
        description=description,
    )
    def aoc_report(context: dg.AssetExecutionContext, duck: DuckDBResource):
        year = context.partition_key
        run_names_str = ",".join(f"'{name}'" for name in runs) or ""
        input_sql = f"""
            SELECT *
            FROM pro
            WHERE run in ({run_names_str}) and cashflow_year = {year}
        """
        extract = execute_duck_sql(input_sql, duck)

        output = aoc_generator(extract, runs)

        context.add_output_metadata(
            {
                "preview": dg.MetadataValue.table(
                    records=[TableRecord(row) for row in output.to_dicts()],
                ),
                "row_count": output.shape[0],
                "column_count": output.shape[1],
            }
        )

    return aoc_report


def load__aoc_configs_from_yaml(yaml_path: str):
    config = yaml.safe_load(open(yaml_path))
    factory_assets = [
        build_aoc_reports(
            name=aoc_config["name"],
            runs=aoc_config["runs"],
            description=aoc_config["description"],
        )
        for aoc_config in config["aoc_reports"]
    ]
    return factory_assets


aoc_assets = load__aoc_configs_from_yaml(r"orchestration\assets\aoc_report_config.yaml")
