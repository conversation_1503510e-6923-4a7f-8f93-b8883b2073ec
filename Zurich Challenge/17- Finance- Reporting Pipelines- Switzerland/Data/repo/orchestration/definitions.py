import dagster as dg

from orchestration.assets import dbt, reports
from orchestration.resources import (
    dbt_resource,
    duck_resource,
)

dbt_analytics_assets = dg.load_assets_from_modules(
    modules=[dbt]
)
report_assets = dg.load_assets_from_modules(modules=[reports])


defs = dg.Definitions(
    assets=[
        *dbt_analytics_assets,
        *report_assets,
    ],
    resources={"dbt": dbt_resource, "duck": duck_resource},
)
