{"data": {"log_version": 3, "version": "=1.9.2"}, "info": {"category": "", "code": "A001", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "info", "msg": "Running with dbt=1.9.2", "name": "MainReportVersion", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:07.256786Z"}}
{"data": {"args": {"cache_selected_only": "False", "debug": "False", "empty": "None", "fail_fast": "False", "indirect_selection": "eager", "introspect": "True", "invocation_command": "dbt parse --quiet", "log_cache_events": "False", "log_format": "json", "log_path": "C:\\Github\\pipelines_challenge\\dbt\\target", "no_print": "None", "partial_parse": "True", "printer_width": "80", "profiles_dir": "C:\\Github\\pipelines_challenge\\dbt", "quiet": "True", "send_anonymous_usage_stats": "False", "static_parser": "True", "target_path": "target", "use_colors": "True", "use_experimental_parser": "False", "version_check": "True", "warn_error": "None", "warn_error_options": "WarnErrorOptions(include=[], exclude=[])", "write_json": "True"}}, "info": {"category": "", "code": "A002", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "debug", "msg": "running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'profiles_dir': 'C:\\\\Github\\\\pipelines_challenge\\\\dbt', 'log_path': 'C:\\\\Github\\\\pipelines_challenge\\\\dbt\\\\target', 'fail_fast': 'False', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'True', 'empty': 'None', 'log_format': 'json', 'invocation_command': 'dbt parse --quiet', 'introspect': 'True', 'static_parser': 'True', 'target_path': 'target', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'send_anonymous_usage_stats': 'False'}", "name": "MainReportArgs", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:07.256786Z"}}
{"data": {"adapter_name": "duckdb", "adapter_version": "=1.9.2"}, "info": {"category": "", "code": "E034", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "info", "msg": "Registered adapter: duckdb=1.9.2", "name": "AdapterRegistered", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:07.712964Z"}}
{"data": {"checksum": "12b12750b70de726cfd89136b8e24afc3f3e77597a97bff40ab7e5f9b39d5e18", "profile": "", "target": "", "vars": "{}", "version": "1.9.2"}, "info": {"category": "", "code": "I025", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "debug", "msg": "checksum: 12b12750b70de726cfd89136b8e24afc3f3e77597a97bff40ab7e5f9b39d5e18, vars: {}, profile: , target: , version: 1.9.2", "name": "StateCheckVarsHash", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:08.162579Z"}}
{"data": {"reason": "saved manifest not found. Starting full parse."}, "info": {"category": "", "code": "I024", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "info", "msg": "Unable to do partial parsing because saved manifest not found. Starting full parse.", "name": "UnableToPartialParse", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:08.165578Z"}}
{"data": {"path": "C:\\Github\\pipelines_challenge\\dbt\\target\\perf_info.json"}, "info": {"category": "", "code": "I010", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "info", "msg": "Performance info: C:\\Github\\pipelines_challenge\\dbt\\target\\perf_info.json", "name": "ParsePerfInfoPath", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:10.085196Z"}}
{"data": {"artifact_path": "C:\\Github\\pipelines_challenge\\dbt\\target\\manifest.json", "artifact_type": "WritableManifest"}, "info": {"category": "", "code": "P001", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "debug", "msg": "Wrote artifact WritableManifest to C:\\Github\\pipelines_challenge\\dbt\\target\\manifest.json", "name": "ArtifactWritten", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:10.283449Z"}}
{"data": {"artifact_path": "C:\\Github\\pipelines_challenge\\dbt\\target\\semantic_manifest.json", "artifact_type": "SemanticManifest"}, "info": {"category": "", "code": "P001", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "debug", "msg": "Wrote artifact SemanticManifest to C:\\Github\\pipelines_challenge\\dbt\\target\\semantic_manifest.json", "name": "ArtifactWritten", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:10.285397Z"}}
{"data": {"command": "dbt parse", "completed_at": "2025-06-10T21:34:10.285397Z", "elapsed": 3.1670265, "success": true}, "info": {"category": "", "code": "Q039", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "debug", "msg": "Command `dbt parse` succeeded at 23:34:10.285397 after 3.17 seconds", "name": "CommandCompleted", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:10.285397Z"}}
{"data": {}, "info": {"category": "", "code": "Z042", "extra": {}, "invocation_id": "a15361b5-f62a-4243-8d42-b6ff8a9b3e5a", "level": "debug", "msg": "Flushing usage events", "name": "FlushEvents", "pid": 33576, "thread": "MainThread", "ts": "2025-06-10T21:34:10.285397Z"}}
{"data": {"log_version": 3, "version": "=1.9.2"}, "info": {"category": "", "code": "A001", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "info", "msg": "Running with dbt=1.9.2", "name": "MainReportVersion", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:12.448605Z"}}
{"data": {"args": {"cache_selected_only": "False", "debug": "False", "empty": "None", "fail_fast": "False", "indirect_selection": "eager", "introspect": "True", "invocation_command": "dbt parse --quiet", "log_cache_events": "False", "log_format": "json", "log_path": "C:\\Github\\pipelines_challenge\\dbt\\target", "no_print": "None", "partial_parse": "True", "printer_width": "80", "profiles_dir": "C:\\Github\\pipelines_challenge\\dbt", "quiet": "True", "send_anonymous_usage_stats": "False", "static_parser": "True", "target_path": "target", "use_colors": "True", "use_experimental_parser": "False", "version_check": "True", "warn_error": "None", "warn_error_options": "WarnErrorOptions(include=[], exclude=[])", "write_json": "True"}}, "info": {"category": "", "code": "A002", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "debug", "msg": "running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'profiles_dir': 'C:\\\\Github\\\\pipelines_challenge\\\\dbt', 'log_path': 'C:\\\\Github\\\\pipelines_challenge\\\\dbt\\\\target', 'fail_fast': 'False', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'True', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'invocation_command': 'dbt parse --quiet', 'introspect': 'True', 'log_format': 'json', 'target_path': 'target', 'static_parser': 'True', 'send_anonymous_usage_stats': 'False'}", "name": "MainReportArgs", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:12.455239Z"}}
{"data": {"adapter_name": "duckdb", "adapter_version": "=1.9.2"}, "info": {"category": "", "code": "E034", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "info", "msg": "Registered adapter: duckdb=1.9.2", "name": "AdapterRegistered", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:13.032201Z"}}
{"data": {"checksum": "12b12750b70de726cfd89136b8e24afc3f3e77597a97bff40ab7e5f9b39d5e18", "profile": "", "target": "", "vars": "{}", "version": "1.9.2"}, "info": {"category": "", "code": "I025", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "debug", "msg": "checksum: 12b12750b70de726cfd89136b8e24afc3f3e77597a97bff40ab7e5f9b39d5e18, vars: {}, profile: , target: , version: 1.9.2", "name": "StateCheckVarsHash", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:13.454372Z"}}
{"data": {"added": 0, "changed": 0, "deleted": 0}, "info": {"category": "", "code": "I040", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "debug", "msg": "Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.", "name": "PartialParsingEnabled", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:13.725876Z"}}
{"data": {}, "info": {"category": "", "code": "I017", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "debug", "msg": "Partial parsing enabled, no changes found, skipping parsing", "name": "PartialParsingSkipParsing", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:13.726873Z"}}
{"data": {"path": "C:\\Github\\pipelines_challenge\\dbt\\target\\perf_info.json"}, "info": {"category": "", "code": "I010", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "info", "msg": "Performance info: C:\\Github\\pipelines_challenge\\dbt\\target\\perf_info.json", "name": "ParsePerfInfoPath", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:13.774653Z"}}
{"data": {"artifact_path": "C:\\Github\\pipelines_challenge\\dbt\\target\\manifest.json", "artifact_type": "WritableManifest"}, "info": {"category": "", "code": "P001", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "debug", "msg": "Wrote artifact WritableManifest to C:\\Github\\pipelines_challenge\\dbt\\target\\manifest.json", "name": "ArtifactWritten", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:13.874686Z"}}
{"data": {"artifact_path": "C:\\Github\\pipelines_challenge\\dbt\\target\\semantic_manifest.json", "artifact_type": "SemanticManifest"}, "info": {"category": "", "code": "P001", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "debug", "msg": "Wrote artifact SemanticManifest to C:\\Github\\pipelines_challenge\\dbt\\target\\semantic_manifest.json", "name": "ArtifactWritten", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:13.880959Z"}}
{"data": {"command": "dbt parse", "completed_at": "2025-06-10T21:47:13.880959Z", "elapsed": 1.6093546, "success": true}, "info": {"category": "", "code": "Q039", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "debug", "msg": "Command `dbt parse` succeeded at 23:47:13.880959 after 1.61 seconds", "name": "CommandCompleted", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:13.880959Z"}}
{"data": {}, "info": {"category": "", "code": "Z042", "extra": {}, "invocation_id": "4518763d-d7b7-4f8a-b849-39552b0cad73", "level": "debug", "msg": "Flushing usage events", "name": "FlushEvents", "pid": 23340, "thread": "MainThread", "ts": "2025-06-10T21:47:13.880959Z"}}
{"data": {"log_version": 3, "version": "=1.9.2"}, "info": {"category": "", "code": "A001", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "info", "msg": "Running with dbt=1.9.2", "name": "MainReportVersion", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:20.403120Z"}}
{"data": {"args": {"cache_selected_only": "False", "debug": "False", "empty": "None", "fail_fast": "False", "indirect_selection": "eager", "introspect": "True", "invocation_command": "dbt parse --quiet", "log_cache_events": "False", "log_format": "json", "log_path": "C:\\Github\\pipelines_challenge\\dbt\\target", "no_print": "None", "partial_parse": "True", "printer_width": "80", "profiles_dir": "C:\\Github\\pipelines_challenge\\dbt", "quiet": "True", "send_anonymous_usage_stats": "False", "static_parser": "True", "target_path": "target", "use_colors": "True", "use_experimental_parser": "False", "version_check": "True", "warn_error": "None", "warn_error_options": "WarnErrorOptions(include=[], exclude=[])", "write_json": "True"}}, "info": {"category": "", "code": "A002", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "debug", "msg": "running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'profiles_dir': 'C:\\\\Github\\\\pipelines_challenge\\\\dbt', 'log_path': 'C:\\\\Github\\\\pipelines_challenge\\\\dbt\\\\target', 'fail_fast': 'False', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'True', 'no_print': 'None', 'log_format': 'json', 'static_parser': 'True', 'introspect': 'True', 'warn_error_options': 'WarnErrorOptions(include=[], exclude=[])', 'target_path': 'target', 'invocation_command': 'dbt parse --quiet', 'send_anonymous_usage_stats': 'False'}", "name": "MainReportArgs", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:20.419358Z"}}
{"data": {"adapter_name": "duckdb", "adapter_version": "=1.9.2"}, "info": {"category": "", "code": "E034", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "info", "msg": "Registered adapter: duckdb=1.9.2", "name": "AdapterRegistered", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:20.906253Z"}}
{"data": {"checksum": "12b12750b70de726cfd89136b8e24afc3f3e77597a97bff40ab7e5f9b39d5e18", "profile": "", "target": "", "vars": "{}", "version": "1.9.2"}, "info": {"category": "", "code": "I025", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "debug", "msg": "checksum: 12b12750b70de726cfd89136b8e24afc3f3e77597a97bff40ab7e5f9b39d5e18, vars: {}, profile: , target: , version: 1.9.2", "name": "StateCheckVarsHash", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:21.270149Z"}}
{"data": {"added": 0, "changed": 0, "deleted": 0}, "info": {"category": "", "code": "I040", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "debug", "msg": "Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.", "name": "PartialParsingEnabled", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:21.567436Z"}}
{"data": {}, "info": {"category": "", "code": "I017", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "debug", "msg": "Partial parsing enabled, no changes found, skipping parsing", "name": "PartialParsingSkipParsing", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:21.568160Z"}}
{"data": {"path": "C:\\Github\\pipelines_challenge\\dbt\\target\\perf_info.json"}, "info": {"category": "", "code": "I010", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "info", "msg": "Performance info: C:\\Github\\pipelines_challenge\\dbt\\target\\perf_info.json", "name": "ParsePerfInfoPath", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:21.640048Z"}}
{"data": {"artifact_path": "C:\\Github\\pipelines_challenge\\dbt\\target\\manifest.json", "artifact_type": "WritableManifest"}, "info": {"category": "", "code": "P001", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "debug", "msg": "Wrote artifact WritableManifest to C:\\Github\\pipelines_challenge\\dbt\\target\\manifest.json", "name": "ArtifactWritten", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:21.764076Z"}}
{"data": {"artifact_path": "C:\\Github\\pipelines_challenge\\dbt\\target\\semantic_manifest.json", "artifact_type": "SemanticManifest"}, "info": {"category": "", "code": "P001", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "debug", "msg": "Wrote artifact SemanticManifest to C:\\Github\\pipelines_challenge\\dbt\\target\\semantic_manifest.json", "name": "ArtifactWritten", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:21.770531Z"}}
{"data": {"command": "dbt parse", "completed_at": "2025-06-10T22:32:21.772534Z", "elapsed": 1.5070018, "success": true}, "info": {"category": "", "code": "Q039", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "debug", "msg": "Command `dbt parse` succeeded at 00:32:21.772534 after 1.51 seconds", "name": "CommandCompleted", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:21.773532Z"}}
{"data": {}, "info": {"category": "", "code": "Z042", "extra": {}, "invocation_id": "306f906e-6139-4eaf-b77a-42f28973add4", "level": "debug", "msg": "Flushing usage events", "name": "FlushEvents", "pid": 9408, "thread": "MainThread", "ts": "2025-06-10T22:32:21.774532Z"}}
