{% docs __overview__ %}

## Data Documentation for Agentic Reporting Pipelines Challenge
Data provided for this challenge is either randomly generated or publicly available. The goal is not a numeric result but rather operational excellence. 

This project is meant to be a starting point for the participating teams. Feel free to extend it further if you need.

Use agents to operate and construct reporting pipelines. Show how agents can be deployed in orchestration layer to help with data flow. Create better experience for both owner and consumer. Build a few reporting pipelines, add inter dependencies, add validation and summary. Unleash your agents and show us how they can help us to build a smooth and resilient reporting pipeline. 

{% enddocs %}
