{{
  config(
    materialized='incremental',
    unique_key='cashflow_year'
  )
}}

WITH calc_output AS (
    SELECT
        run,
        cashflow_year,
        SUM(cashflow_present_value_current_rate) cashflow_present_value_current_rate,
        category
    FROM {{ref('stg_pro')}}
    GROUP BY cashflow_year, run, category
    ORDER BY 2,1
) 

SELECT *
FROM calc_output
{% if is_incremental() %}
    WHERE cashflow_year = '{{ var('year') }}'
{% endif %}