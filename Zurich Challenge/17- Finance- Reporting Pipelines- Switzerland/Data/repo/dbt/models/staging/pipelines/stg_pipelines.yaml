version: 2

models:
  - name: stg_mpf1
    description: This table contains basic information about portfolio of customers on the contract level
    columns:
      - name: id
        description: Unique ID
        tests:
          - not_null
      - name: year
        description: Year of portfolio extract 
      - name: portfolio
        description: Letter representing portfolio/product type
      - name: premium
        description: Premium paid by a specific contract
      - name: contract_duration
        description: Length of a contract
      - name: INIT_MEM_IF
        description: Number of insured under the contract
  - name: stg_mpf2
    description: This table contains basic information about portfolio of customers on the contract level
    columns:
      - name: id
        description: Unique ID
        tests:
          - not_null
      - name: year
        description: Year of portfolio extract 
      - name: portfolio
        description: Letter representing portfolio/product type
      - name: premium
        description: Premium paid by a specific contract
      - name: contract_duration
        description: Length of a contract
      - name: INIT_MEM_IF
        description: Number of insured under the contract
  - name: stg_pro
    description: This table contains results from calculation engine projecting cash flows on the whole portfolio level
    columns:
      - name: run
        description: Calculation name
      - name: cashflow_year
        description: Projection start year, different cashflow year mean calculation done at a different point in time
      - name: cashflow_month
        description: Projection start month
      - name: prophet_variable
        description: Variable from calculation engine
      - name: cashflow_amount
        description: Cashflow value
      - name: cashflow_present_value_current_rate
        description: Discounted value of future cashflows to cashflow_year and cashflow_month
      - name: cashflow_present_value_undiscounted
        description: Undiscounted (sum) of future cashflows to cashflow_year and cashflow_month
      - name: category
        description: Descriptive category, e.g. premiums, outgoes, asset value, expenses, etc.