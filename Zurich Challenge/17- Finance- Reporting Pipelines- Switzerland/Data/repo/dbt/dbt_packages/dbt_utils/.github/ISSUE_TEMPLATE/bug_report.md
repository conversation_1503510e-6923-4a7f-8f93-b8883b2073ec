---
name: Bug report
about: Report a bug or an issue you've found with this package
title: ''
labels: bug, triage
assignees: ''

---

### Describe the bug
<!---
A clear and concise description of what the bug is. You can also use the issue title to do this
--->

### Steps to reproduce
<!---
In as much detail as possible, please provide steps to reproduce the issue. Sample data that triggers the issue, example model code, etc is all very helpful here.
--->

### Expected results
<!---
A clear and concise description of what you expected to happen.
--->

### Actual results
<!---
A clear and concise description of what you expected to happen.
--->

### Screenshots and log output
<!---
If applicable, add screenshots or log output to help explain your problem.
--->

### System information
**The contents of your `packages.yml` file:**

**Which database are you using dbt with?**
- [ ] postgres
- [ ] redshift
- [ ] bigquery
- [ ] snowflake
- [ ] other (specify: ____________)


**The output of `dbt --version`:**
```
<output goes here>
```


### Additional context
<!---
Add any other context about the problem here. For example, if you think you know which line of code is causing the issue.
--->

### Are you interested in contributing the fix?
<!---
Let us know if you want to contribute the fix, and whether would need a hand getting started
--->
