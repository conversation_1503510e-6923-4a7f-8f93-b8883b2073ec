# This is a basic workflow to help you get started with Actions

name: Update table of contents

# Controls when the workflow will run
on:
  push:
    branches: [main]
    paths: ['README.md']

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v3
      - run: |
          curl https://raw.githubusercontent.com/ekalinin/github-markdown-toc/master/gh-md-toc -o gh-md-toc
          chmod a+x gh-md-toc
          ./gh-md-toc --insert --no-backup README.md
          rm ./gh-md-toc
      - uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: Auto update table of contents
