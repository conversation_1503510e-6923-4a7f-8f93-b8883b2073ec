[project]
name = "pipelines-challenge"
version = "0.0.1"
description = "Agentic AI Hyperchallenge, reporting pipelines"
readme = "Getting-Started.md"
requires-python = ">=3.9"
dependencies = [
    "dagster==1.10.*",
    "dagster-webserver",
    "dagster-cloud",
    "dagster-duckdb",
    "dagster-dbt",
    "dbt-duckdb==1.9.2",
    "geopandas",
    "pandas[parquet]",
    "shapely",
    "matplotlib",
    "smart_open[s3]",
    "s3fs",
    "smart_open",
    "boto3",
    "pyarrow",
    "openpyxl>=3.1.5",
    "polars>=1.30.0",
    "ruff>=0.9.3",
    "pytest>=8.3.4",
]

[tool.uv]
dev-dependencies = [
    "ruff",
    "pytest",
    "pip-licenses>=5.0.0",
]

[tool.dagster]
module_name = "orchestration.definitions"
code_location_name = "Agentic-Pipelines"

[project.scripts]
lint = "ruff check --fix ."
format = "ruff format ."
check = "ruff check . && ruff format --check ."