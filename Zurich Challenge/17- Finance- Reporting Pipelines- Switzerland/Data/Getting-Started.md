<h1>Table of Contents</h1>

- [Contact Person](#contact-person)
- [Objective](#objective)
- [Data Package](#data-package)
  - [Schemas](#schemas)
- [Next steps](#next-steps)
  - [Option 1 - Open Source Setup](#option-1---open-source-setup)
  - [Option 2 - Bring your own stack](#option-2---bring-your-own-stack)
- [Evaluation criteria](#evaluation-criteria)
    - [General Notes](#general-notes)
- [Q\&A](#qa)

# Contact Person

Subject matter expert for this use case:
<PERSON><PERSON>

# Objective

Use agents to operate and construct reporting pipelines. Show how agents can be deployed in orchestration layer to help with data flow. Create better experience for both owner and consumer. Build a few reporting pipelines, add inter dependencies, add validation and summary. Unleash your agents and show us how they can help us to build a smooth and resilient reporting pipeline. ​

Key features:
  * Operate ETL (Extract-Transform-Load) pipeline with minimal human intervention​
  * Setup reporting process based on requirements ​
  * Allow management to request specific information​
  * Create pipeline on request 

Data provided for this challenge is either randomly generated or publicly available. The goal is not a numeric result but rather operational excellence. 

<a id="features-details"></a>
Features we are looking for in more detail:
1. Core
   1. Provide status information on the reporting and where to find specific outputs with some highlights.
   2. Be able to query details about reporting process. Use pipelines as a documentation.
   3. Consume PDF input with reporting requirements and set up/flag elements that have to be executed <a id="rep-req"></a>
   4. Execute pipelines
   5. Meta data tracking, flag outliers
   6. Clearly show Agent's actions and interventions
2. Really nice
   1. Setup new reporting pipelines
   2. Show that agents can fix issues with inputs
      1. Type errors
      2. Changes to the naming convention
      3. Missing data
   3. Prepare audit trail
3. Extras
   1. Anything that you think could bring value

# Data Package

Data provided for this challenge:

| File/Folder                |                                                                                                                                                                                                               Description |   Format |
| :------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | -------: |
| Interest_Rates             | Folder contains sample information about interest rates at specific time points. Public information, more can be downloaded [here](https://www.eiopa.europa.eu/tools-and-data/risk-free-interest-rate-term-structures_en) |    .xlsx |
| 2024_Q4_CLOSE_INSTRUCTIONS |                                                 Extract from the latest, full-year reporting [requirements](#rep-req). treat it as an input that agents could use to set up relevant pipelines. Also source of meta data. |     .pdf |
| mock_mpf_data_1            |                                                                                                                                      Dummy policy information, years 2021-2024. Schema information will be provided below |     .csv |
| mock_mpf_data_2            |                                                                                                                                      Dummy policy information, years 2021-2024. Schema information will be provided below |    .xlsx |
| mock_pro_data              |                                                                                                                         Dummy extract from calculation engine, years 2021-2024. Schema information will be provided below | .parquet |
| repo                       |                                                                           Sample pipelines based on the above data build with open source packages - optional. You can go with your own tools and build pipelines as well |        - |

## Schemas

Dummy policy information:
```python
        schema={
            'id': str, # Unique ID
            'year': int, # Information as at end of year
            'portfolio': str, # Letter representing a portfolio/product type
            'premium': float, # Premium paid by a specific contract           
            'contract_duration': int, # Length of a contract
            'INIT_MEM_IF': int, # Number of insured under the contract
            }
```

Dummy extract from calculation engine:
```python
        schema={
            'run':str, # Calculation name
            'cashflow_year':int, # Projection start year, different cashflow year mean calculation done at a different point in time
            'cashflow_month':int, # Month of a cashflow
            'prophet_variable':str, # Variable from calculation engine
            'cashflow_amount':float, # Cashflow value
            'cashflow_present_value_current_rate':float, # Discounted value of future cashflows to cashflow_year and cashflow_month
            'cashflow_present_value_undiscounted': float, # Undiscounted (sum) of future cashflows to cashflow_year year and cashflow_month
            'category': str # Descriptive category, premiums|outgoes|asset value|expenses etc.
        }
```

# Next steps

## Option 1 - Open Source Setup

1. Go to repo folder
2. Install uv - [installation guide](https://docs.astral.sh/uv/getting-started/installation/)
3. Run:
    ```powershell
    uv sync
    ```   
4. Install dbt dependencies:
    ```powershell
    cd dbt
    uv run dbt deps
    ```    

You have just set up a small pipeline exemplar based on the open source tech stack:
- [DuckDB](https://duckdb.org/docs/stable/)
- [dbt Core](https://docs.getdbt.com/docs/core/about-core-setup)
- [Dagster](https://docs.dagster.io/)

Check it out by running:

**dbt** - transformation workflow

```powershell 
cd dbt
uv run dbt docs generate
uv run dbt docs serve
```

**Dagster** - orchestrator

Go back to the main repo folder

```powershell 
uv run dagster dev
```
If things went well you should see something like this:

<img src="images\dagster_ui.jpeg" alt="Dagster UI" width="1000" height="600">

Now you have an access to data along with small pipelines and environment to experiment with. This is your starting point, hack away!

## Option 2 - Bring your own stack

Take provided inputs and create a few reporting pipelines.

What package/software to use for that? Anything that we would be able to experiment with and you'll be able to show us. If this would incur additional costs for the pilot please let us know. You can also use some parts of exemplar from open source section. 

Build agents on top and show us what is possible!

# Evaluation criteria

Your submission will be evaluated against [feature list](#features-details). Features are split between core and extra category. Core features have priority.

Please bare in mind that we will have limited time to review your submissions. Make sure to prepare it in a way 
that will make it easy for us to do so. If your solution is the best but we can't see it it will not be selected. 
### General Notes

Our expectation would be to see something between 3 and 10 reports with interdependencies. Again the numbers are not important so don't really worry if they mean anything.

Think if number of reports you're showing is enough to showcase the power of your agents. When preparing a submission please do not forget to include information about limitations and potential bottlenecks.

  * Would it be as efficient with 100 reports as it is with 5, what is the scaling limitation.
  * What is the expected running cost
  * How would you scope a pilot project, what milestones and challenges do you see?
  * Please submit detailed reports summarizing technical requirements for successful adoption.

# Q&A

This section will be updated as we go along. 

Q: I would need more data to showcase something, **can I create it myself**?

A: **Absolutely**, be mindful that we will need to test it afterwards. What it means is we could ask for a script to generate test data. For simplicity and ease of use lets make sure that any extra data will be generated with **python** and is compatible with **version 3.10**

Q: I need **more metadata**

A: **Feel free to create it**. One of the goals would be to understand what and how much information we need to give to the agents such that they can differentiate between specific reports.

Q: Can I modify/extend/add components to the exemplar?

A: Again, **absolutely**. You'd like to add a dashboard, extra report, functionality - please do so. Exemplar is just a way to get you started faster but it should not limit you. Feel free to change it in any way that you see fit.

Q: I need to get in touch with Subject Matter Expert

A: Make sure that your question is not something that can be answered via Teams Channel. If you still need a 1-1 session request a meeting vai teams. Be specific, be prepared, add detailed description of the problem and why you need in person meeting in your message.

