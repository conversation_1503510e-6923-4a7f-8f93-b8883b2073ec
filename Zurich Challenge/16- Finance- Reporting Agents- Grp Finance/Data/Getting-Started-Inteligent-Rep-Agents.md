<h1>Table of Contents</h1>

- [Contact Person](#contact-person)
- [Objective](#objective)
- [Data Package](#data-package)
  - [Data set 1 - Data on the group level of aggregation](#data-set-1---data-on-the-group-level-of-aggregation)
    - [Schemas](#schemas)
  - [Data set 2 - BU perspective](#data-set-2---bu-perspective)
    - [Schemas](#schemas-1)
- [Next steps](#next-steps)
- [Evaluation criteria](#evaluation-criteria)
    - [General Notes](#general-notes)
- [Q\&A](#qa)

# Contact Person

Subject matter expert for this use case:

Lead: <PERSON>

Support: <PERSON><PERSON>

# Objective

Deploy agents to create reports and presentations with or without a template. Provide descriptive statistics and fill out the gaps. Show that agents can understand the data and can operate on independent data sets. Use reasoning and data to challenge the main points of the presentations and reports ​

<a id="success-criteria"></a>
Key features:
  * Access data and create detailed reports
    * Create summaries on different levels of aggregation- ex. business unit, segment, LoB, portfolio.
  * Update presentation template with relevant data from the database
    * Extract relevant information from the database and update the presentation template. Provide descriptive statistics for the extracted data. 
  * Perform Analysis of Change
    * The goal of AoC is to provide a clear understanding of why financial or actuarial results have changed (i.e.: identify factors, quantify impact, changes to drivers and contributors) 
  * Use data to challenge the AoC report
    * Use critical thinking to question and challenge the results. Look for internal contradictions or data manipulation (variance, trend, anomalies, scenarios analysis). Compare internal metrics with external information.
  * Provide executive summary of reports
    * Summarize the main findings, highlighting areas that need further clarification and areas that are showing reasonable movements. Rationals for all items in each list should be also reported (why investigation is needed or why the movements is well explained).
  * Analyze report and presentation from completeness perspective 
    * Use the data to generate insights not included in the report/presentation. Look for gaps and blind spots.

Features in more detail:

| Task                                       | Detailed Description                                                                        | Capabilities Required                                 |
| :----------------------------------------- | :------------------------------------------------------------------------------------------ | :---------------------------------------------------- |
| Access local data                          | Retrieve data from business units                                                           | Querying                                              |
| Identify trends                            | Analyze data to identify patterns or trends                                                 | Statistical analysis                                  |
| Find outliers and unexpected movements     | Detect data manipulation, fraud, data quality issues                                        | Anomaly detection                                     |
| Compare key metrics to market benchmarks   | Compare internal metrics with external market standards                                     | Web Search / Querying & Data Comparison               |
| Generate report per BU                     | Create detailed reports for each business unit linking movements with drivers               | Report generation (Text, Data etc.)                   |
| Generate aggregated regional reports       | Combine data from different BUs into regional reports                                       | Report generation (Text, Data etc.)                   |
| Highlight key findings per report          | Emphasize important insights and results                                                    | Summarization of the report                           |
| Outline areas under control per report     | Outline the metrics for the areas that are well-managed explaining why it is reasonable     | Summarization of the report                           |
| Outline areas for investigation per report | Identify areas needing further investigation and questions to be addressed                  | Summarization of the report & question formulation    |
| Presentation building                      | Update presentation template with relevant data from the database                           | Query and understand presentation context             |
| Presentation building                      | Provide descriptive analysis                                                                | Query and arithmetic operations                       |
| Presentation building                      | Use data to challenge the presentation outcome                                              | Query, common sense, logic, check vs sensitivities    |
| Presentation building                      | Modify presentation template as required                                                    | Ability to write code                                 |
| Presentation building                      | Create a presentation without a template, ask for things that are not in the usual analysis | Ability to write code, creative and critical thinking |

# Data Package

Data provided for this challenge:

## Data set 1 - Data on the group level of aggregation

| File/Folder                               |                                                                              Description | Format |
| :---------------------------------------- | ---------------------------------------------------------------------------------------: | -----: |
| Hackathon Exhibit commentary              | Extract from the usual reporting presentation - this is the template you need to fill in |   pptx |
| AgenticAI_Hachathon_2022_2023aggregated 3 |                                      Data set used to populate the presentation template |    csv |

### Schemas

AgenticAI_Hachathon_2022_2023aggregated 3:
```python
schema = {
    "Fiscal Year": int,  # One-year period that companies use for financial reporting.
    "Quarter": int,  # Three-month period on a company's financial calendar that acts as a basis for periodic financial reports.
    "Main Process": str,  # Process flag to identify the data for the reporting of the quarter-close results.
    "Node_masked": str,  # Organizational structure of the company's business units (countries) for financial reporting. (Masked)
    "BucketName": str,  # Split for CAY and PAY data.
    "BookingType": str,  # Process flag to identify if data is submitted by business units or it is taken from financial systems.
    "LoB_masked": str,  # Line of Business refers to insurance products which are grouped into categories based on the type and nature of the products. (Masked)
    "MainsegmentNode": str,  # Grouping of the business based on the customers - individuals vs companies.
    "AccidentYear": int,  # The year in which an insurance claims occurred, regardless of when the claim is reported or settled (closed).
    "PaidLossandALAE": float,  # Actual payments made for claims and the expenses directly associated with settling those claims. ALAE - allocated loss adjustment expenses.
    "PaidULAE": float,  # The expenses related to claim handling that cannot be directly attributed to a specific claim. ULAE - unallocated loss adjustment expenses.
    "CaseReserves": float,  # The funds set aside by an insurer to pay for reported claims that have not yet been settled (closed).
    "ActuarialIBNR": float,  # The estimated liabilities for claims that have occurred but have not yet been reported to the insurer. IBNR - incurred but not reported.
    "SpecIBNR": float,  # The estimated liabilities for claims that have occurred but have not yet been reported to the insurer. IBNR - incurred but not reported.
    "ULAEReserves": float,  # The funds set aside to cover the general expenses of handling claims that cannot be attributed to specific claims. ULAE - unallocated loss adjustment expenses.
    "AllowanceLimitedHistExp": float,  # The adjustments made to reserves based on the limited historical data available.
    "OtherIfrs17ShortTermLiabilities": float,  # Funds set aside to cover liabilities not included in claims reserves or expenses.
    "NDIC": float,  # A component of an insurance contract that must be repaid to the policyholder in all circumstances. NDIC - non-distinct investment component.
    "PolicyholderDividends": float,  # The returns or profits distributed to policyholders from the surplus generated by the insurer.
    "RiskOfNonPerformance": float,  # The risk that a counterparty (e.g. reinsurer) will not fulfill its contractual obligations, in monetary terms.
    "OtherIfrs17Reserves": float,  # The funds set aside to meet obligations under the IFRS17 (International Financial Reporting Standard 17) which are not covered by other reserves.
    "AttritionalLosses": float,  # The regular, expected losses that occur in the normal course of business, excluding large or catastrophic events.
    "LargeLosses2": float,  # The significant losses that exceed a certain threshold, typically categorized separately for reporting purposes.
    "LargeLosses1": float,  # The significant losses that exceed a certain threshold, typically categorized separately for reporting purposes.
    "CatLosses": float,  # The losses resulting from large-scale events such as natural disasters, which have a significant impact on the insurer's financial results.
    "WeatherLosses": float,  # The losses resulting from weather-related events such as storms, floods, and hurricanes.
    "OtherLosses": float,  # The losses that do not fall into the predefined categories and are reported separately.
    "ULAE": float,  # The general expenses related to claim handling that cannot be attributed to specific claims. ULAE - unallocated loss adjustment expenses
    "NonCatLosses": float,  # The losses that are not related to large-scale catastrophic events.
    "PYDActuarialNominal": float,  # The change in the estimated claims losses from prior accident years. PYD - prior year development.
    "PYDULAE": float,  # The change in the estimated ULAE losses from prior accident years. PYD - prior year development.
    "ChangeInOtherIfrs17ShortTermLiabilities": float,  # A change in Other IFRS 17 short-term liabilities between two reporting quarters.
    "ChangeInReservesForPolicyHolderDividends": float,  # A change in Policyholder Dividends between two reporting quarters.
    "NominalReserves": float,  # The funds set aside by an insurer to cover future claims, based on the nominal value without considering the time value of money.
    "DiscountedReservesLI": float,  # The present value of future claim liabilities, discounted with interest rate corresponding to the accident year of the claim.
    "DiscountedReserves": float,  # The present value of future claim liabilities, discounted with interest rate corresponding to the fiscal year and quarter.
    "RiskAdjustmentLi": float,  # The additional amount added to the discounted reserves (at locked-in rates) to account for the uncertainty in the estimation of future claim liabilities.
    "RiskAdjustmentCurrent": float,  # The additional amount added to the discounted reserves (at current rates) to account for the uncertainty in the estimation of future claim liabilities.
    "DiscEffectonPYDChangeinPP": float,  # The impact on discount due to changes to the expected timing of future claim payments on prior accident years. PP - payment pattern.
    "DiscEffectonPYDExperienceadjOSLosses": float,  # The impact on discount due to changes to the estimated losses from prior accident years.
    "ActualPaid": float,  # The actual amount of claims paid during the reporting period.
    "ExpectedPaid": float,  # The expected amount of claims to be paid during the reporting period based on actuarial estimates.
    "DiscEffectonPYDPaidBalancingItem": float,  # Balancing item that covers the remaining impact on the discount.
    "OCL": float,  # Funds sets aside to cover insurance contracts that are expected to be loss-making (claims costs and expenses exceeding the premium income). OCL - onerous contract liability.
    "Unwind": float,  # The gradual reduction of discount over time.
    "InterestRateChange": float,  # The impact of changes in interest rates on the valuation of the liabilities.
    "RiskAdjustmentUnwind": float,  # The gradual reduction of discount over time, attributable to risk adjustment.
    "RiskAdjustmentInterestRateChange": float,  # The impact of changes in interest rates on the risk adjustment.
    "AOCI": float,  # Funds that cover unrealised gains or losses caused by the movements in interest rates. AOCI - Accumulated Other Comprehensive Income.
    "RiskAdjustmentAoci": float,  # Funds that cover unrealised gains or losses on risk adjustment caused by the movements in interest rates. AOCI - Accumulated Other Comprehensive Income.
    "ActualIncurred": float,  # Sum of actual claims paid and the claims reserves.
    "ExpectedIncurred": float,  # Sum of expected claims paid and the claims reserves.
    "PydFromAvePaid": float,  # The difference between the actual claims paid and the expected claims paid for prior accident years. It highlights deviations in the timing of of claim payments or claim amounts compared to what was originally estimated.
    "PydFromAveReserve": float,  # The difference between the actual loss reserves and the expected reserves for prior accident years. It reflects changes in the estimated future claims liabilities due to updated information or revised assumptions.
    "ChangeInNDIC": float,  # A change in NDIC between two reporting quarters.
    "ChangeInReservesForPolicyholderDividends": float,  # A change in Policyholder Dividends between two reporting quarters.
    "ChangeInOcl": float,  # A change in OCL between two reporting quarters.
    "DiscountImpact": float,  # Difference between discounted reserves and nominal reserves.
}

```

## Data set 2 - BU perspective

| File/Folder                   |                                                                                                                                                                     Description | Format |
| :---------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------: | -----: |
| IFRS17 - IL - 2025Q1 - AI PoC |                                                                                        Extract from the usual reporting presentation - this is the template you need to fill in |   pptx |
| IFRS17 - IL - 2025Q1 - AI PoC |                                             Extract from the usual reporting presentation - this is the template you need to fill in but in pdf format so you know how it looks |    pdf |
| SICAF IFRS17 AoC (AI PoC)     | PowerBI report and your database - includes several years of reporting data. There are quite a few data sources there but the main table of interest is **ice_results_prepped** |   pbix |
 
### Schemas

ice_results_prepped:
```python
schema={
    'ice_movement_type': str, # Movement type identifier (e.g., IFRS17_Bop_MA)
    'metric_value': float, # Primary metric value amount
    'ice_metric_kind': str, # Metric classification (e.g., OPN_present_value_amount_CreditSpread)
    'close_period': str, # Reporting period date (e.g., 01-Dec-24)
    'configuration_name': str, # Reporting period identifier (e.g., {24Q4} Actual)
    'CSM Profit By Source': str, # CSM profit source classification
    'CSM AoC Level 2': str, # CSM Analysis of Change category
    'CSM AoC': str,
    'group_of_contracts': str, # Contract group identifier (e.g., Agg:Endowment)
    'annual_cohort': int, # Year of contract cohort
    'Accounting Approach': str, # Accounting method (e.g., VFA, BAA)
    'RA AoC': str, # Risk Adjustment Analysis of Change
    'RA Components': str, # Risk Adjustment component breakdown
    'pnl_value': float, # Profit and Loss value amount
    'IACF AoC': str, # Insurance Acquisition Cash Flow Analysis of Change
    'CU_BoP': float, # Coverage Units Beginning of Period
    'CU_ExpEoP': float, # Coverage Units Expected End of Period
    'CU_EoP': float, # Coverage Units End of Period
    'GroupOfContracts': str, # Group of contracts classification
    'Experience Adjustment Cashflows': str, # Experience adjustment cash flow type
    'Cashflow Source': str, # Source of cash flow
    'ice_metric_kind (groups)': str, # Metric kind classification
    'if_or_new_cohort': str, # In-force or new cohort indicator
    'PVFCF_Components': str, # Present Value of Future Cash Flows components
    'nb_opn_mutualization_weights': float, # New business mutualization weights
    'SubGoC': str, # Sub-group of contracts
    'thisEffectiveAmortizationRate': float, # Effective amortization rate
    '_CSMAmortization': float, # CSM amortization amount
    'vfa_flag': int, # Variable Fee Approach flag (0/1)
    'UIComponent': str, # Underlying Item component
    'UnderlyingItem': float, # Underlying item value
    'GeneratesExpected': str, # Generates expected indicator
    '_PVFCF': str, # Present Value of Future Cash Flows
    '_pvfcf_bop': str, # PVFCF Beginning of Period
    '_PVFCF_CF': str, # PVFCF Cash Flow
    '_PVFCF_IA': str, # PVFCF Insurance Acquisition
    '_PVFCF_amount': str, # PVFCF amount classification
    '_PVFCF_NBView': str, # PVFCF New Business view
    'VariableFee': float, # Variable fee amount
    'NegPVFCF': str, # Negative PVFCF amount
    '_PVFCF_PxS': float, # PVFCF Profit by source
    'metric_value_ave': float, # Average metric value
    'Cashflow type': str, # Type of cash flow
    'ice_movement_type (groups)': str, # Grouped movement type
    '_IMK_SAPExpenses': str, # IMK SAP expenses classification
    'IMC_Summary': str, # IMC summary category
    'Summary AoC Steps': str, # Summary Analysis of Change steps
    'Cohort Generations': str, # Cohort generation grouping (e.g., 2005-2009)
    'CoverageUnits': str, # Coverage units classification
    'processing_group': str, # Processing group identifier (e.g., IL - individual life)
}
```
# Next steps

1. Take either of the data sets as your starting point.
2. Use presentation templates as a reference point and data guide. By matching the content of the presentation with database you should be able to identify the key variables of interest.
3. I would suggest starting with a task of filling in presentation templates. While building this feature is still on a simpler side it will also deepen your understanding of the data. Of course if you would rather focus on something else you are free to do so.

In case of BU perspective there is one additional step required. Presentation is directly linked to the powerBI report. In order to see it as in the pdf example provided you'd have to publish the report first and subsequently link presentation to the report you just published.

The same solution should work equally well on both dataset as we expect it to be independent from the data. This is necessary for Zurich wide adoption.

# Evaluation criteria

Your submission will be evaluated against [key features](#success-criteria). For the task of creating a report, filing in presentation template and creating a summary we would expect close to perfect (99%) accuracy. We would also expect that presentation template does not get adjusted outside of commentary. It should maintain order and appropriate level of detail. 

As we get to creating AoC report we would want to see not only the final output but also steps agents took to arrive there. This is also true for the remainder of the tasks, having this information would allow an easy sens check for human reviewer.

Please bare in mind that we will have limited time to review your submissions. Make sure to prepare it in a way that will make it easy for us to do so. If your solution is the best but we can't see it it will not be selected.

### General Notes

You have been given two data sets. We are interested in seeing how your agents can generalize and adopt to solve the same problems but in slightly different context.

When preparing a submission please do not forget to include information about limitations and potential bottlenecks.

  * Are there any limitations in terms of how much data can be reliably processed?
  * What are the ways we could improve documentation and presentations such that LLM's and agents can work with them better?
  * What is the expected running cost
  * How would you scope a pilot project, what milestones and challenges do you see?
  * Don't forget to showcase performance on both datasets if you managed to make it work. If it only works to some extent it's also a valuable information. If you'll show how reporting agents work on the single data set we will assume they don't work at all on the other.
  * Please submit detailed reports summarizing technical requirements for successful adoption.

# Q&A

This section will be updated as we go along. 

Q: I need to get in touch with Subject Matter Expert

A: Make sure that your question is not something that can be answered via Teams Channel. If you still need a 1-1 session request a meeting vai teams. Be specific, be prepared, add detailed description of the problem and why you need in person meeting in your message.

Q: Can I use different presentation tool than Power Point?

A: Yes but PP is the most common in the Zurich, in case two solutions work equally well the one that uses PP will be rated higher. 

