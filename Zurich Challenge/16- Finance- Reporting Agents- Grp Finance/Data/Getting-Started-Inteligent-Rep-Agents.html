<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Contact Person</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1>Table of Contents</h1>
<ul>
<li><a href="#contact-person">Contact Person</a></li>
<li><a href="#objective">Objective</a></li>
<li><a href="#data-package">Data Package</a>
<ul>
<li><a href="#data-set-1---data-on-the-group-level-of-aggregation">Data set 1 - Data on the group level of aggregation</a>
<ul>
<li><a href="#schemas">Schemas</a></li>
</ul>
</li>
<li><a href="#data-set-2---bu-perspective">Data set 2 - BU perspective</a>
<ul>
<li><a href="#schemas-1">Schemas</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#next-steps">Next steps</a></li>
<li><a href="#evaluation-criteria">Evaluation criteria</a>
<ul>
<li><a href="#general-notes">General Notes</a></li>
</ul>
</li>
<li><a href="#qa">Q&amp;A</a></li>
</ul>
<h1 id="contact-person">Contact Person</h1>
<p>Subject matter expert for this use case:</p>
<p>Lead: Francesco Valente</p>
<p>Support: Tomasz Skorkowski</p>
<h1 id="objective">Objective</h1>
<p>Deploy agents to create reports and presentations with or without a template. Provide descriptive statistics and fill out the gaps. Show that agents can understand the data and can operate on independent data sets. Use reasoning and data to challenge the main points of the presentations and reports ​</p>
<p><a id="success-criteria"></a>
Key features:</p>
<ul>
<li>Access data and create detailed reports
<ul>
<li>Create summaries on different levels of aggregation- ex. business unit, segment, LoB, portfolio.</li>
</ul>
</li>
<li>Update presentation template with relevant data from the database
<ul>
<li>Extract relevant information from the database and update the presentation template. Provide descriptive statistics for the extracted data.</li>
</ul>
</li>
<li>Perform Analysis of Change
<ul>
<li>The goal of AoC is to provide a clear understanding of why financial or actuarial results have changed (i.e.: identify factors, quantify impact, changes to drivers and contributors)</li>
</ul>
</li>
<li>Use data to challenge the AoC report
<ul>
<li>Use critical thinking to question and challenge the results. Look for internal contradictions or data manipulation (variance, trend, anomalies, scenarios analysis). Compare internal metrics with external information.</li>
</ul>
</li>
<li>Provide executive summary of reports
<ul>
<li>Summarize the main findings, highlighting areas that need further clarification and areas that are showing reasonable movements. Rationals for all items in each list should be also reported (why investigation is needed or why the movements is well explained).</li>
</ul>
</li>
<li>Analyze report and presentation from completeness perspective
<ul>
<li>Use the data to generate insights not included in the report/presentation. Look for gaps and blind spots.</li>
</ul>
</li>
</ul>
<p>Features in more detail:</p>
<table>
<thead>
<tr>
<th style="text-align:left">Task</th>
<th style="text-align:left">Detailed Description</th>
<th style="text-align:left">Capabilities Required</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">Access local data</td>
<td style="text-align:left">Retrieve data from business units</td>
<td style="text-align:left">Querying</td>
</tr>
<tr>
<td style="text-align:left">Identify trends</td>
<td style="text-align:left">Analyze data to identify patterns or trends</td>
<td style="text-align:left">Statistical analysis</td>
</tr>
<tr>
<td style="text-align:left">Find outliers and unexpected movements</td>
<td style="text-align:left">Detect data manipulation, fraud, data quality issues</td>
<td style="text-align:left">Anomaly detection</td>
</tr>
<tr>
<td style="text-align:left">Compare key metrics to market benchmarks</td>
<td style="text-align:left">Compare internal metrics with external market standards</td>
<td style="text-align:left">Web Search / Querying &amp; Data Comparison</td>
</tr>
<tr>
<td style="text-align:left">Generate report per BU</td>
<td style="text-align:left">Create detailed reports for each business unit linking movements with drivers</td>
<td style="text-align:left">Report generation (Text, Data etc.)</td>
</tr>
<tr>
<td style="text-align:left">Generate aggregated regional reports</td>
<td style="text-align:left">Combine data from different BUs into regional reports</td>
<td style="text-align:left">Report generation (Text, Data etc.)</td>
</tr>
<tr>
<td style="text-align:left">Highlight key findings per report</td>
<td style="text-align:left">Emphasize important insights and results</td>
<td style="text-align:left">Summarization of the report</td>
</tr>
<tr>
<td style="text-align:left">Outline areas under control per report</td>
<td style="text-align:left">Outline the metrics for the areas that are well-managed explaining why it is reasonable</td>
<td style="text-align:left">Summarization of the report</td>
</tr>
<tr>
<td style="text-align:left">Outline areas for investigation per report</td>
<td style="text-align:left">Identify areas needing further investigation and questions to be addressed</td>
<td style="text-align:left">Summarization of the report &amp; question formulation</td>
</tr>
<tr>
<td style="text-align:left">Presentation building</td>
<td style="text-align:left">Update presentation template with relevant data from the database</td>
<td style="text-align:left">Query and understand presentation context</td>
</tr>
<tr>
<td style="text-align:left">Presentation building</td>
<td style="text-align:left">Provide descriptive analysis</td>
<td style="text-align:left">Query and arithmetic operations</td>
</tr>
<tr>
<td style="text-align:left">Presentation building</td>
<td style="text-align:left">Use data to challenge the presentation outcome</td>
<td style="text-align:left">Query, common sense, logic, check vs sensitivities</td>
</tr>
<tr>
<td style="text-align:left">Presentation building</td>
<td style="text-align:left">Modify presentation template as required</td>
<td style="text-align:left">Ability to write code</td>
</tr>
<tr>
<td style="text-align:left">Presentation building</td>
<td style="text-align:left">Create a presentation without a template, ask for things that are not in the usual analysis</td>
<td style="text-align:left">Ability to write code, creative and critical thinking</td>
</tr>
</tbody>
</table>
<h1 id="data-package">Data Package</h1>
<p>Data provided for this challenge:</p>
<h2 id="data-set-1---data-on-the-group-level-of-aggregation">Data set 1 - Data on the group level of aggregation</h2>
<table>
<thead>
<tr>
<th style="text-align:left">File/Folder</th>
<th style="text-align:right">Description</th>
<th style="text-align:right">Format</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">Hackathon Exhibit commentary</td>
<td style="text-align:right">Extract from the usual reporting presentation - this is the template you need to fill in</td>
<td style="text-align:right">pptx</td>
</tr>
<tr>
<td style="text-align:left">AgenticAI_Hachathon_2022_2023aggregated 3</td>
<td style="text-align:right">Data set used to populate the presentation template</td>
<td style="text-align:right">csv</td>
</tr>
</tbody>
</table>
<h3 id="schemas">Schemas</h3>
<p>AgenticAI_Hachathon_2022_2023aggregated 3:</p>
<pre><code class="language-python">schema = {
    <span class="hljs-string">&quot;Fiscal Year&quot;</span>: <span class="hljs-built_in">int</span>,  <span class="hljs-comment"># One-year period that companies use for financial reporting.</span>
    <span class="hljs-string">&quot;Quarter&quot;</span>: <span class="hljs-built_in">int</span>,  <span class="hljs-comment"># Three-month period on a company&#x27;s financial calendar that acts as a basis for periodic financial reports.</span>
    <span class="hljs-string">&quot;Main Process&quot;</span>: <span class="hljs-built_in">str</span>,  <span class="hljs-comment"># Process flag to identify the data for the reporting of the quarter-close results.</span>
    <span class="hljs-string">&quot;Node_masked&quot;</span>: <span class="hljs-built_in">str</span>,  <span class="hljs-comment"># Organizational structure of the company&#x27;s business units (countries) for financial reporting. (Masked)</span>
    <span class="hljs-string">&quot;BucketName&quot;</span>: <span class="hljs-built_in">str</span>,  <span class="hljs-comment"># Split for CAY and PAY data.</span>
    <span class="hljs-string">&quot;BookingType&quot;</span>: <span class="hljs-built_in">str</span>,  <span class="hljs-comment"># Process flag to identify if data is submitted by business units or it is taken from financial systems.</span>
    <span class="hljs-string">&quot;LoB_masked&quot;</span>: <span class="hljs-built_in">str</span>,  <span class="hljs-comment"># Line of Business refers to insurance products which are grouped into categories based on the type and nature of the products. (Masked)</span>
    <span class="hljs-string">&quot;MainsegmentNode&quot;</span>: <span class="hljs-built_in">str</span>,  <span class="hljs-comment"># Grouping of the business based on the customers - individuals vs companies.</span>
    <span class="hljs-string">&quot;AccidentYear&quot;</span>: <span class="hljs-built_in">int</span>,  <span class="hljs-comment"># The year in which an insurance claims occurred, regardless of when the claim is reported or settled (closed).</span>
    <span class="hljs-string">&quot;PaidLossandALAE&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># Actual payments made for claims and the expenses directly associated with settling those claims. ALAE - allocated loss adjustment expenses.</span>
    <span class="hljs-string">&quot;PaidULAE&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The expenses related to claim handling that cannot be directly attributed to a specific claim. ULAE - unallocated loss adjustment expenses.</span>
    <span class="hljs-string">&quot;CaseReserves&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The funds set aside by an insurer to pay for reported claims that have not yet been settled (closed).</span>
    <span class="hljs-string">&quot;ActuarialIBNR&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The estimated liabilities for claims that have occurred but have not yet been reported to the insurer. IBNR - incurred but not reported.</span>
    <span class="hljs-string">&quot;SpecIBNR&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The estimated liabilities for claims that have occurred but have not yet been reported to the insurer. IBNR - incurred but not reported.</span>
    <span class="hljs-string">&quot;ULAEReserves&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The funds set aside to cover the general expenses of handling claims that cannot be attributed to specific claims. ULAE - unallocated loss adjustment expenses.</span>
    <span class="hljs-string">&quot;AllowanceLimitedHistExp&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The adjustments made to reserves based on the limited historical data available.</span>
    <span class="hljs-string">&quot;OtherIfrs17ShortTermLiabilities&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># Funds set aside to cover liabilities not included in claims reserves or expenses.</span>
    <span class="hljs-string">&quot;NDIC&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># A component of an insurance contract that must be repaid to the policyholder in all circumstances. NDIC - non-distinct investment component.</span>
    <span class="hljs-string">&quot;PolicyholderDividends&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The returns or profits distributed to policyholders from the surplus generated by the insurer.</span>
    <span class="hljs-string">&quot;RiskOfNonPerformance&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The risk that a counterparty (e.g. reinsurer) will not fulfill its contractual obligations, in monetary terms.</span>
    <span class="hljs-string">&quot;OtherIfrs17Reserves&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The funds set aside to meet obligations under the IFRS17 (International Financial Reporting Standard 17) which are not covered by other reserves.</span>
    <span class="hljs-string">&quot;AttritionalLosses&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The regular, expected losses that occur in the normal course of business, excluding large or catastrophic events.</span>
    <span class="hljs-string">&quot;LargeLosses2&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The significant losses that exceed a certain threshold, typically categorized separately for reporting purposes.</span>
    <span class="hljs-string">&quot;LargeLosses1&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The significant losses that exceed a certain threshold, typically categorized separately for reporting purposes.</span>
    <span class="hljs-string">&quot;CatLosses&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The losses resulting from large-scale events such as natural disasters, which have a significant impact on the insurer&#x27;s financial results.</span>
    <span class="hljs-string">&quot;WeatherLosses&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The losses resulting from weather-related events such as storms, floods, and hurricanes.</span>
    <span class="hljs-string">&quot;OtherLosses&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The losses that do not fall into the predefined categories and are reported separately.</span>
    <span class="hljs-string">&quot;ULAE&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The general expenses related to claim handling that cannot be attributed to specific claims. ULAE - unallocated loss adjustment expenses</span>
    <span class="hljs-string">&quot;NonCatLosses&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The losses that are not related to large-scale catastrophic events.</span>
    <span class="hljs-string">&quot;PYDActuarialNominal&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The change in the estimated claims losses from prior accident years. PYD - prior year development.</span>
    <span class="hljs-string">&quot;PYDULAE&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The change in the estimated ULAE losses from prior accident years. PYD - prior year development.</span>
    <span class="hljs-string">&quot;ChangeInOtherIfrs17ShortTermLiabilities&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># A change in Other IFRS 17 short-term liabilities between two reporting quarters.</span>
    <span class="hljs-string">&quot;ChangeInReservesForPolicyHolderDividends&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># A change in Policyholder Dividends between two reporting quarters.</span>
    <span class="hljs-string">&quot;NominalReserves&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The funds set aside by an insurer to cover future claims, based on the nominal value without considering the time value of money.</span>
    <span class="hljs-string">&quot;DiscountedReservesLI&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The present value of future claim liabilities, discounted with interest rate corresponding to the accident year of the claim.</span>
    <span class="hljs-string">&quot;DiscountedReserves&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The present value of future claim liabilities, discounted with interest rate corresponding to the fiscal year and quarter.</span>
    <span class="hljs-string">&quot;RiskAdjustmentLi&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The additional amount added to the discounted reserves (at locked-in rates) to account for the uncertainty in the estimation of future claim liabilities.</span>
    <span class="hljs-string">&quot;RiskAdjustmentCurrent&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The additional amount added to the discounted reserves (at current rates) to account for the uncertainty in the estimation of future claim liabilities.</span>
    <span class="hljs-string">&quot;DiscEffectonPYDChangeinPP&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The impact on discount due to changes to the expected timing of future claim payments on prior accident years. PP - payment pattern.</span>
    <span class="hljs-string">&quot;DiscEffectonPYDExperienceadjOSLosses&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The impact on discount due to changes to the estimated losses from prior accident years.</span>
    <span class="hljs-string">&quot;ActualPaid&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The actual amount of claims paid during the reporting period.</span>
    <span class="hljs-string">&quot;ExpectedPaid&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The expected amount of claims to be paid during the reporting period based on actuarial estimates.</span>
    <span class="hljs-string">&quot;DiscEffectonPYDPaidBalancingItem&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># Balancing item that covers the remaining impact on the discount.</span>
    <span class="hljs-string">&quot;OCL&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># Funds sets aside to cover insurance contracts that are expected to be loss-making (claims costs and expenses exceeding the premium income). OCL - onerous contract liability.</span>
    <span class="hljs-string">&quot;Unwind&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The gradual reduction of discount over time.</span>
    <span class="hljs-string">&quot;InterestRateChange&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The impact of changes in interest rates on the valuation of the liabilities.</span>
    <span class="hljs-string">&quot;RiskAdjustmentUnwind&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The gradual reduction of discount over time, attributable to risk adjustment.</span>
    <span class="hljs-string">&quot;RiskAdjustmentInterestRateChange&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The impact of changes in interest rates on the risk adjustment.</span>
    <span class="hljs-string">&quot;AOCI&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># Funds that cover unrealised gains or losses caused by the movements in interest rates. AOCI - Accumulated Other Comprehensive Income.</span>
    <span class="hljs-string">&quot;RiskAdjustmentAoci&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># Funds that cover unrealised gains or losses on risk adjustment caused by the movements in interest rates. AOCI - Accumulated Other Comprehensive Income.</span>
    <span class="hljs-string">&quot;ActualIncurred&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># Sum of actual claims paid and the claims reserves.</span>
    <span class="hljs-string">&quot;ExpectedIncurred&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># Sum of expected claims paid and the claims reserves.</span>
    <span class="hljs-string">&quot;PydFromAvePaid&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The difference between the actual claims paid and the expected claims paid for prior accident years. It highlights deviations in the timing of of claim payments or claim amounts compared to what was originally estimated.</span>
    <span class="hljs-string">&quot;PydFromAveReserve&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># The difference between the actual loss reserves and the expected reserves for prior accident years. It reflects changes in the estimated future claims liabilities due to updated information or revised assumptions.</span>
    <span class="hljs-string">&quot;ChangeInNDIC&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># A change in NDIC between two reporting quarters.</span>
    <span class="hljs-string">&quot;ChangeInReservesForPolicyholderDividends&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># A change in Policyholder Dividends between two reporting quarters.</span>
    <span class="hljs-string">&quot;ChangeInOcl&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># A change in OCL between two reporting quarters.</span>
    <span class="hljs-string">&quot;DiscountImpact&quot;</span>: <span class="hljs-built_in">float</span>,  <span class="hljs-comment"># Difference between discounted reserves and nominal reserves.</span>
}

</code></pre>
<h2 id="data-set-2---bu-perspective">Data set 2 - BU perspective</h2>
<table>
<thead>
<tr>
<th style="text-align:left">File/Folder</th>
<th style="text-align:right">Description</th>
<th style="text-align:right">Format</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">IFRS17 - IL - 2025Q1 - AI PoC</td>
<td style="text-align:right">Extract from the usual reporting presentation - this is the template you need to fill in</td>
<td style="text-align:right">pptx</td>
</tr>
<tr>
<td style="text-align:left">IFRS17 - IL - 2025Q1 - AI PoC</td>
<td style="text-align:right">Extract from the usual reporting presentation - this is the template you need to fill in but in pdf format so you know how it looks</td>
<td style="text-align:right">pdf</td>
</tr>
<tr>
<td style="text-align:left">SICAF IFRS17 AoC (AI PoC)</td>
<td style="text-align:right">PowerBI report and your database - includes several years of reporting data. There are quite a few data sources there but the main table of interest is <strong>ice_results_prepped</strong></td>
<td style="text-align:right">pbix</td>
</tr>
</tbody>
</table>
<h3 id="schemas-1">Schemas</h3>
<p>ice_results_prepped:</p>
<pre><code class="language-python">schema={
    <span class="hljs-string">&#x27;ice_movement_type&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Movement type identifier (e.g., IFRS17_Bop_MA)</span>
    <span class="hljs-string">&#x27;metric_value&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Primary metric value amount</span>
    <span class="hljs-string">&#x27;ice_metric_kind&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Metric classification (e.g., OPN_present_value_amount_CreditSpread)</span>
    <span class="hljs-string">&#x27;close_period&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Reporting period date (e.g., 01-Dec-24)</span>
    <span class="hljs-string">&#x27;configuration_name&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Reporting period identifier (e.g., {24Q4} Actual)</span>
    <span class="hljs-string">&#x27;CSM Profit By Source&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># CSM profit source classification</span>
    <span class="hljs-string">&#x27;CSM AoC Level 2&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># CSM Analysis of Change category</span>
    <span class="hljs-string">&#x27;CSM AoC&#x27;</span>: <span class="hljs-built_in">str</span>,
    <span class="hljs-string">&#x27;group_of_contracts&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Contract group identifier (e.g., Agg:Endowment)</span>
    <span class="hljs-string">&#x27;annual_cohort&#x27;</span>: <span class="hljs-built_in">int</span>, <span class="hljs-comment"># Year of contract cohort</span>
    <span class="hljs-string">&#x27;Accounting Approach&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Accounting method (e.g., VFA, BAA)</span>
    <span class="hljs-string">&#x27;RA AoC&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Risk Adjustment Analysis of Change</span>
    <span class="hljs-string">&#x27;RA Components&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Risk Adjustment component breakdown</span>
    <span class="hljs-string">&#x27;pnl_value&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Profit and Loss value amount</span>
    <span class="hljs-string">&#x27;IACF AoC&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Insurance Acquisition Cash Flow Analysis of Change</span>
    <span class="hljs-string">&#x27;CU_BoP&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Coverage Units Beginning of Period</span>
    <span class="hljs-string">&#x27;CU_ExpEoP&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Coverage Units Expected End of Period</span>
    <span class="hljs-string">&#x27;CU_EoP&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Coverage Units End of Period</span>
    <span class="hljs-string">&#x27;GroupOfContracts&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Group of contracts classification</span>
    <span class="hljs-string">&#x27;Experience Adjustment Cashflows&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Experience adjustment cash flow type</span>
    <span class="hljs-string">&#x27;Cashflow Source&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Source of cash flow</span>
    <span class="hljs-string">&#x27;ice_metric_kind (groups)&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Metric kind classification</span>
    <span class="hljs-string">&#x27;if_or_new_cohort&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># In-force or new cohort indicator</span>
    <span class="hljs-string">&#x27;PVFCF_Components&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Present Value of Future Cash Flows components</span>
    <span class="hljs-string">&#x27;nb_opn_mutualization_weights&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># New business mutualization weights</span>
    <span class="hljs-string">&#x27;SubGoC&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Sub-group of contracts</span>
    <span class="hljs-string">&#x27;thisEffectiveAmortizationRate&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Effective amortization rate</span>
    <span class="hljs-string">&#x27;_CSMAmortization&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># CSM amortization amount</span>
    <span class="hljs-string">&#x27;vfa_flag&#x27;</span>: <span class="hljs-built_in">int</span>, <span class="hljs-comment"># Variable Fee Approach flag (0/1)</span>
    <span class="hljs-string">&#x27;UIComponent&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Underlying Item component</span>
    <span class="hljs-string">&#x27;UnderlyingItem&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Underlying item value</span>
    <span class="hljs-string">&#x27;GeneratesExpected&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Generates expected indicator</span>
    <span class="hljs-string">&#x27;_PVFCF&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Present Value of Future Cash Flows</span>
    <span class="hljs-string">&#x27;_pvfcf_bop&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># PVFCF Beginning of Period</span>
    <span class="hljs-string">&#x27;_PVFCF_CF&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># PVFCF Cash Flow</span>
    <span class="hljs-string">&#x27;_PVFCF_IA&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># PVFCF Insurance Acquisition</span>
    <span class="hljs-string">&#x27;_PVFCF_amount&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># PVFCF amount classification</span>
    <span class="hljs-string">&#x27;_PVFCF_NBView&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># PVFCF New Business view</span>
    <span class="hljs-string">&#x27;VariableFee&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Variable fee amount</span>
    <span class="hljs-string">&#x27;NegPVFCF&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Negative PVFCF amount</span>
    <span class="hljs-string">&#x27;_PVFCF_PxS&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># PVFCF Profit by source</span>
    <span class="hljs-string">&#x27;metric_value_ave&#x27;</span>: <span class="hljs-built_in">float</span>, <span class="hljs-comment"># Average metric value</span>
    <span class="hljs-string">&#x27;Cashflow type&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Type of cash flow</span>
    <span class="hljs-string">&#x27;ice_movement_type (groups)&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Grouped movement type</span>
    <span class="hljs-string">&#x27;_IMK_SAPExpenses&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># IMK SAP expenses classification</span>
    <span class="hljs-string">&#x27;IMC_Summary&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># IMC summary category</span>
    <span class="hljs-string">&#x27;Summary AoC Steps&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Summary Analysis of Change steps</span>
    <span class="hljs-string">&#x27;Cohort Generations&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Cohort generation grouping (e.g., 2005-2009)</span>
    <span class="hljs-string">&#x27;CoverageUnits&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Coverage units classification</span>
    <span class="hljs-string">&#x27;processing_group&#x27;</span>: <span class="hljs-built_in">str</span>, <span class="hljs-comment"># Processing group identifier (e.g., IL - individual life)</span>
}
</code></pre>
<h1 id="next-steps">Next steps</h1>
<ol>
<li>Take either of the data sets as your starting point.</li>
<li>Use presentation templates as a reference point and data guide. By matching the content of the presentation with database you should be able to identify the key variables of interest.</li>
<li>I would suggest starting with a task of filling in presentation templates. While building this feature is still on a simpler side it will also deepen your understanding of the data. Of course if you would rather focus on something else you are free to do so.</li>
</ol>
<p>In case of BU perspective there is one additional step required. Presentation is directly linked to the powerBI report. In order to see it as in the pdf example provided you'd have to publish the report first and subsequently link presentation to the report you just published.</p>
<p>The same solution should work equally well on both dataset as we expect it to be independent from the data. This is necessary for Zurich wide adoption.</p>
<h1 id="evaluation-criteria">Evaluation criteria</h1>
<p>Your submission will be evaluated against <a href="#success-criteria">key features</a>. For the task of creating a report, filing in presentation template and creating a summary we would expect close to perfect (99%) accuracy. We would also expect that presentation template does not get adjusted outside of commentary. It should maintain order and appropriate level of detail.</p>
<p>As we get to creating AoC report we would want to see not only the final output but also steps agents took to arrive there. This is also true for the remainder of the tasks, having this information would allow an easy sens check for human reviewer.</p>
<p>Please bare in mind that we will have limited time to review your submissions. Make sure to prepare it in a way that will make it easy for us to do so. If your solution is the best but we can't see it it will not be selected.</p>
<h3 id="general-notes">General Notes</h3>
<p>You have been given two data sets. We are interested in seeing how your agents can generalize and adopt to solve the same problems but in slightly different context.</p>
<p>When preparing a submission please do not forget to include information about limitations and potential bottlenecks.</p>
<ul>
<li>Are there any limitations in terms of how much data can be reliably processed?</li>
<li>What are the ways we could improve documentation and presentations such that LLM's and agents can work with them better?</li>
<li>What is the expected running cost</li>
<li>How would you scope a pilot project, what milestones and challenges do you see?</li>
<li>Don't forget to showcase performance on both datasets if you managed to make it work. If it only works to some extent it's also a valuable information. If you'll show how reporting agents work on the single data set we will assume they don't work at all on the other.</li>
<li>Please submit detailed reports summarizing technical requirements for successful adoption.</li>
</ul>
<h1 id="qa">Q&amp;A</h1>
<p>This section will be updated as we go along.</p>
<p>Q: I need to get in touch with Subject Matter Expert</p>
<p>A: Make sure that your question is not something that can be answered via Teams Channel. If you still need a 1-1 session request a meeting vai teams. Be specific, be prepared, add detailed description of the problem and why you need in person meeting in your message.</p>
<p>Q: Can I use different presentation tool than Power Point?</p>
<p>A: Yes but PP is the most common in the Zurich, in case two solutions work equally well the one that uses PP will be rated higher.</p>

            
            
        </body>
        </html>